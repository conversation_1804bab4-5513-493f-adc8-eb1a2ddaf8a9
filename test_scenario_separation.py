#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแยกข้อมูลตาม scenario เพื่อแก้ปัญหาค่าพารามิเตอร์เหมือนกัน
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_scenario_data_separation():
    """ทดสอบการแยกข้อมูลตาม scenario"""
    print("=== ทดสอบการแยกข้อมูลตาม scenario ===")
    print(f"เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        from python_LightGBM_17_Signal import (
            filter_data_by_scenario,
            get_default_threshold_by_scenario,
            get_default_nbars_by_scenario,
            load_validation_data_for_optimization
        )
        
        print("✅ Import ฟังก์ชันสำเร็จ")
        
        # ทดสอบการโหลดข้อมูล validation
        symbol = "GOLD"
        timeframe = 30
        
        print(f"\n🔍 ทดสอบการโหลดข้อมูล validation สำหรับ {symbol} M{timeframe}")
        val_df = load_validation_data_for_optimization(symbol, timeframe)
        
        if val_df is None:
            print(f"❌ ไม่สามารถโหลดข้อมูล validation สำหรับ {symbol} M{timeframe}")
            return False
        
        print(f"✅ โหลดข้อมูลสำเร็จ: {val_df.shape}")
        print(f"📊 Columns: {list(val_df.columns)[:10]}...")  # แสดง 10 columns แรก
        
        # ทดสอบการแยกข้อมูลตาม scenario
        scenarios = ['trend_following', 'counter_trend']
        
        for scenario in scenarios:
            print(f"\n📊 ทดสอบการแยกข้อมูลสำหรับ {scenario}")
            
            # แยกข้อมูล
            filtered_df = filter_data_by_scenario(val_df, scenario)
            
            print(f"   ข้อมูลเดิม: {len(val_df)} samples")
            print(f"   ข้อมูลที่กรอง: {len(filtered_df)} samples")
            print(f"   สัดส่วน: {len(filtered_df)/len(val_df)*100:.1f}%")
            
            if 'Target' in filtered_df.columns:
                target_dist = filtered_df['Target'].value_counts()
                print(f"   Target distribution: {target_dist.to_dict()}")
        
        # ทดสอบค่า default
        print(f"\n🎯 ทดสอบค่า default threshold และ nBars_SL")
        
        symbols = ['GOLD', 'AUDUSD', 'EURUSD', 'USDJPY']
        timeframes = [30, 60]
        
        for symbol in symbols:
            for timeframe in timeframes:
                print(f"\n💰 {symbol} M{timeframe}:")
                
                for scenario in scenarios:
                    threshold = get_default_threshold_by_scenario(scenario, symbol)
                    nbars = get_default_nbars_by_scenario(scenario, symbol, timeframe)
                    
                    print(f"   {scenario}: threshold={threshold:.3f}, nBars_SL={nbars}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_filtering_conditions():
    """ทดสอบเงื่อนไขการกรองข้อมูล"""
    print("\n=== ทดสอบเงื่อนไขการกรองข้อมูล ===")
    
    # สร้างข้อมูลทดสอบ
    np.random.seed(42)
    n_samples = 1000
    
    test_data = {
        'Close': np.random.uniform(1800, 2000, n_samples),
        'EMA_200': np.random.uniform(1750, 1950, n_samples),
        'RSI_14': np.random.uniform(20, 80, n_samples),
        'MACD': np.random.uniform(-5, 5, n_samples),
        'ADX_14': np.random.uniform(10, 50, n_samples),
        'Target': np.random.choice([0, 1], n_samples)
    }
    
    test_df = pd.DataFrame(test_data)
    print(f"📊 สร้างข้อมูลทดสอบ: {test_df.shape}")
    
    try:
        from python_LightGBM_17_Signal import filter_data_by_scenario
        
        # ทดสอบ trend_following filter
        trend_df = filter_data_by_scenario(test_df, 'trend_following')
        print(f"📈 Trend Following: {len(trend_df)}/{len(test_df)} samples ({len(trend_df)/len(test_df)*100:.1f}%)")
        
        # ทดสอบ counter_trend filter
        counter_df = filter_data_by_scenario(test_df, 'counter_trend')
        print(f"📉 Counter Trend: {len(counter_df)}/{len(test_df)} samples ({len(counter_df)/len(test_df)*100:.1f}%)")
        
        # ตรวจสอบว่าข้อมูลแตกต่างกัน
        overlap = len(set(trend_df.index) & set(counter_df.index))
        print(f"🔄 Overlap: {overlap} samples ({overlap/len(test_df)*100:.1f}%)")
        
        if len(trend_df) != len(counter_df) or overlap < len(test_df) * 0.8:
            print("✅ การกรองข้อมูลทำงานถูกต้อง - ได้ข้อมูลที่แตกต่างกัน")
        else:
            print("⚠️ การกรองข้อมูลอาจไม่ได้ผล - ข้อมูลยังคล้ายกันมาก")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบการกรอง: {e}")
        return False

def test_run_optimization_with_separation():
    """ทดสอบการรันการหาค่าที่เหมาะสมด้วยการแยกข้อมูล"""
    print("\n=== ทดสอบการรันการหาค่าที่เหมาะสมด้วยการแยกข้อมูล ===")
    
    try:
        from python_LightGBM_17_Signal import run_multi_model_optimization
        
        # ทดสอบกับ GOLD M30
        symbol = "GOLD"
        timeframe = 30
        
        print(f"🚀 ทดสอบการหาค่าที่เหมาะสมสำหรับ {symbol} M{timeframe}")
        print("⚠️ การทดสอบนี้อาจใช้เวลานาน...")
        
        # รันการหาค่าที่เหมาะสม
        result = run_multi_model_optimization(symbol, timeframe)
        
        if result:
            print("✅ การหาค่าที่เหมาะสมสำเร็จ")
            print(f"📊 ผลลัพธ์:")
            print(f"   Symbol: {result['symbol']}")
            print(f"   Timeframe: {result['timeframe']}")
            print(f"   Scenarios: {result['scenarios']}")
            
            for scenario in result['scenarios']:
                threshold = result['optimal_thresholds'].get(scenario, 'N/A')
                nbars = result['optimal_nbars'].get(scenario, 'N/A')
                print(f"   {scenario}: threshold={threshold}, nBars_SL={nbars}")
            
            # ตรวจสอบว่าค่าแตกต่างกันหรือไม่
            thresholds = list(result['optimal_thresholds'].values())
            nbars_values = list(result['optimal_nbars'].values())
            
            threshold_diff = len(set(thresholds)) > 1
            nbars_diff = len(set(nbars_values)) > 1
            
            if threshold_diff or nbars_diff:
                print("✅ ค่าพารามิเตอร์แตกต่างกันระหว่าง scenarios")
            else:
                print("⚠️ ค่าพารามิเตอร์ยังคงเหมือนกัน - อาจต้องปรับปรุงเพิ่มเติม")
            
            return True
        else:
            print("❌ การหาค่าที่เหมาะสมไม่สำเร็จ")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ optimization: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔍 เริ่มการทดสอบการแยกข้อมูลตาม scenario")
    print("=" * 60)
    
    # ทดสอบแต่ละส่วน
    tests = [
        ("การแยกข้อมูลตาม scenario", test_scenario_data_separation),
        ("เงื่อนไขการกรองข้อมูล", test_data_filtering_conditions),
        # ("การรันการหาค่าที่เหมาะสม", test_run_optimization_with_separation),  # Comment ไว้เพราะใช้เวลานาน
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name}: {'ผ่าน' if result else 'ไม่ผ่าน'}")
        except Exception as e:
            print(f"❌ {test_name}: เกิดข้อผิดพลาด - {e}")
            results.append((test_name, False))
    
    # สรุปผลการทดสอบ
    print(f"\n{'='*60}")
    print("📊 สรุปผลการทดสอบ:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 ผลรวม: {passed}/{total} การทดสอบผ่าน")
    
    if passed == total:
        print("🎉 การทดสอบทั้งหมดผ่าน! ระบบพร้อมใช้งาน")
    else:
        print("⚠️ มีการทดสอบที่ไม่ผ่าน กรุณาตรวจสอบและแก้ไข")

if __name__ == "__main__":
    main()
