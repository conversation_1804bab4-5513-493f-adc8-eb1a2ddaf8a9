#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขขั้นสุดท้าย - บังคับใช้ค่า scenario-specific
"""

import sys
import os
import glob
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def clean_and_test():
    """ลบไฟล์เก่าและทดสอบการหาค่าที่เหมาะสมใหม่"""
    print("🔧 การทดสอบการแก้ไขขั้นสุดท้าย")
    print("=" * 60)
    print(f"⏰ เวลาเริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ลบไฟล์เก่า
    print("\n🧹 ลบไฟล์พารามิเตอร์เก่า")
    threshold_files = glob.glob("LightGBM_Multi/thresholds/*_optimal_threshold.pkl")
    nbars_files = glob.glob("LightGBM_Multi/thresholds/*_optimal_nBars_SL.pkl")
    
    deleted_count = 0
    for file_path in threshold_files + nbars_files:
        try:
            os.remove(file_path)
            print(f"🗑️ ลบ: {os.path.basename(file_path)}")
            deleted_count += 1
        except:
            pass
    
    print(f"✅ ลบไฟล์เสร็จสิ้น: {deleted_count} ไฟล์")
    
    # ทดสอบค่า default
    print("\n📊 ทดสอบค่า default")
    try:
        from python_LightGBM_17_Signal import (
            get_default_threshold_by_scenario,
            get_default_nbars_by_scenario
        )
        
        symbol = "GOLD"
        timeframe = 30
        scenarios = ['trend_following', 'counter_trend']
        
        print(f"💰 {symbol} M{timeframe}:")
        for scenario in scenarios:
            threshold = get_default_threshold_by_scenario(scenario, symbol)
            nbars = get_default_nbars_by_scenario(scenario, symbol, timeframe)
            print(f"   {scenario}: threshold={threshold:.3f}, nBars_SL={nbars}")
        
        print("✅ ค่า default แตกต่างกัน")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบค่า default: {e}")
        return False
    
    # ทดสอบการหาค่าที่เหมาะสม
    print("\n🚀 ทดสอบการหาค่าที่เหมาะสมใหม่")
    try:
        from python_LightGBM_17_Signal import run_multi_model_optimization
        
        symbol = "GOLD"
        timeframe = 30
        
        print(f"🔍 กำลังหา optimal parameters สำหรับ {symbol} M{timeframe}")
        print("⚠️ การทดสอบนี้อาจใช้เวลา 2-3 นาที...")
        
        start_time = datetime.now()
        result = run_multi_model_optimization(symbol, timeframe)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        print(f"\n⏱️ เวลาที่ใช้: {duration:.1f} วินาที")
        
        if result:
            print("\n✅ การหาค่าที่เหมาะสมสำเร็จ!")
            print(f"📊 ผลลัพธ์สำหรับ {symbol} M{timeframe}:")
            
            thresholds = []
            nbars_values = []
            
            for scenario in result['scenarios']:
                threshold = result['optimal_thresholds'].get(scenario, 'N/A')
                nbars = result['optimal_nbars'].get(scenario, 'N/A')
                print(f"   {scenario}: threshold={threshold}, nBars_SL={nbars}")
                
                if threshold != 'N/A':
                    thresholds.append(threshold)
                if nbars != 'N/A':
                    nbars_values.append(nbars)
            
            # ตรวจสอบว่าค่าแตกต่างกันหรือไม่
            threshold_diff = len(set(thresholds)) > 1 if len(thresholds) >= 2 else False
            nbars_diff = len(set(nbars_values)) > 1 if len(nbars_values) >= 2 else False
            
            print(f"\n🔍 การวิเคราะห์ความแตกต่าง:")
            print(f"   Threshold values: {thresholds}")
            print(f"   nBars_SL values: {nbars_values}")
            print(f"   Threshold แตกต่างกัน: {'✅ ใช่' if threshold_diff else '❌ ไม่'}")
            print(f"   nBars_SL แตกต่างกัน: {'✅ ใช่' if nbars_diff else '❌ ไม่'}")
            
            success = threshold_diff or nbars_diff
            
            if success:
                print("\n🎉 สำเร็จ! ค่าพารามิเตอร์แตกต่างกันระหว่าง scenarios แล้ว")
            else:
                print("\n⚠️ ค่าพารามิเตอร์ยังคงเหมือนกัน")
            
            # ตรวจสอบพารามิเตอร์ที่บันทึก
            print("\n🔍 ตรวจสอบพารามิเตอร์ที่บันทึก")
            try:
                from python_LightGBM_17_Signal import (
                    load_scenario_threshold,
                    load_scenario_nbars
                )
                
                saved_thresholds = []
                saved_nbars = []
                
                for scenario in ['trend_following', 'counter_trend']:
                    try:
                        threshold = load_scenario_threshold(symbol, timeframe, scenario)
                        nbars = load_scenario_nbars(symbol, timeframe, scenario)
                        
                        saved_thresholds.append(threshold)
                        saved_nbars.append(nbars)
                        
                        print(f"   {scenario}: threshold={threshold:.4f}, nBars_SL={nbars}")
                        
                    except Exception as e:
                        print(f"   ❌ {scenario}: ไม่สามารถโหลดได้ ({e})")
                
                if len(saved_thresholds) == 2 and len(saved_nbars) == 2:
                    saved_threshold_diff = saved_thresholds[0] != saved_thresholds[1]
                    saved_nbars_diff = saved_nbars[0] != saved_nbars[1]
                    
                    print(f"\n📊 พารามิเตอร์ที่บันทึก:")
                    print(f"   Threshold แตกต่างกัน: {'✅ ใช่' if saved_threshold_diff else '❌ ไม่'}")
                    print(f"   nBars_SL แตกต่างกัน: {'✅ ใช่' if saved_nbars_diff else '❌ ไม่'}")
                    
                    final_success = saved_threshold_diff or saved_nbars_diff
                    
                    print(f"\n🎯 ผลสุดท้าย: {'✅ สำเร็จ' if final_success else '❌ ยังไม่สำเร็จ'}")
                    
                    if final_success:
                        print("\n🎉 การแก้ไขสำเร็จ! ปัญหาได้รับการแก้ไขแล้ว")
                        print("\n📋 ขั้นตอนถัดไป:")
                        print("   1. รันการหาค่าที่เหมาะสมสำหรับ symbols อื่นๆ")
                        print("   2. อัปเดต MT5 WebRequest Server")
                        print("   3. ทดสอบการเทรดจริง")
                        print("   4. สร้างรายงานสรุปใหม่")
                    else:
                        print("\n💡 แนวทางเพิ่มเติม:")
                        print("   1. ปรับปรุงอัลกอริทึมการหาค่าที่เหมาะสม")
                        print("   2. เพิ่มข้อมูล features ที่ขาดหายไป")
                        print("   3. ปรับเงื่อนไขการกรองข้อมูล")
                    
                    return final_success
                else:
                    print("⚠️ ไม่สามารถโหลดพารามิเตอร์ครบถ้วน")
                    return False
                    
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาดในการตรวจสอบ: {e}")
                return False
            
        else:
            print("❌ การหาค่าที่เหมาะสมไม่สำเร็จ")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print(f"\n⏰ เวลาสิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    clean_and_test()
