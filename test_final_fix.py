#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Final Fix Script
สคริปต์ทดสอบการแก้ไขสุดท้ายสำหรับปัญหาการบันทึกผลลัพธ์ Multi-Model
"""

import subprocess
import sys
import time
import os
from datetime import datetime

def run_test_and_check_results():
    """รันการทดสอบและตรวจสอบผลลัพธ์"""
    print("🧪 ทดสอบการแก้ไขสุดท้าย")
    print("=" * 50)
    
    command = "python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30"
    print(f"🚀 รันคำสั่ง: {command}")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 80)
    
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        output_lines = []
        start_time = time.time()
        
        # ตัวแปรสำหรับตรวจสอบ
        filter_working = False
        multi_model_save = False
        training_completed = False
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                print(line)
                output_lines.append(line)
                
                # ตรวจสอบการกรองข้อมูล
                if "📊 ผลการกรอง: 1 กลุ่ม, รวม 1 ไฟล์" in line:
                    filter_working = True
                    print("\n✅ การกรองข้อมูลทำงานถูกต้อง!")
                
                # ตรวจสอบการบันทึกผลลัพธ์ Multi-Model
                if "✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น (Multi-Model)" in line:
                    multi_model_save = True
                    print("\n✅ การบันทึกผลลัพธ์ Multi-Model ทำงานถูกต้อง!")
                
                # ตรวจสอบการเทรนเสร็จสิ้น
                if "🎯 ผลสรุป: 4/4 การตั้งค่าทดสอบสำเร็จ" in line:
                    training_completed = True
                    print("\n✅ การเทรนทั้ง 4 การตั้งค่าเสร็จสิ้น!")
                
                # หยุดเมื่อเห็นข้อความที่แสดงว่าการแก้ไขทำงาน
                if training_completed and multi_model_save:
                    print(f"\n✅ ทดสอบเสร็จสิ้น - การแก้ไขทำงานถูกต้อง")
                    time.sleep(2)  # รอให้การเปรียบเทียบเสร็จ
                    process.terminate()
                    break
                
                # หยุดหากใช้เวลานานเกินไป
                if time.time() - start_time > 600:  # 10 นาที
                    print(f"\n⏰ Timeout หลังจาก 10 นาที")
                    process.terminate()
                    break
        
        # วิเคราะห์ผลลัพธ์
        print("\n" + "=" * 80)
        print("📊 สรุปผลการทดสอบ:")
        print(f"   การกรองข้อมูล: {'✅ ทำงานถูกต้อง' if filter_working else '❌ ไม่ทำงาน'}")
        print(f"   การบันทึก Multi-Model: {'✅ ทำงานถูกต้อง' if multi_model_save else '❌ ไม่ทำงาน'}")
        print(f"   การเทรนเสร็จสิ้น: {'✅ ทำงานถูกต้อง' if training_completed else '❌ ไม่ทำงาน'}")
        
        return filter_working and multi_model_save and training_completed
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")
        return False

def check_saved_files():
    """ตรวจสอบไฟล์ที่บันทึกแล้ว"""
    print("\n🔍 ตรวจสอบไฟล์ที่บันทึกแล้ว")
    print("=" * 50)
    
    configs = [
        "config_1_macd_deep",
        "config_2_macd_signal", 
        "config_3_enhanced_deep",
        "config_4_enhanced_signal"
    ]
    
    found_files = {}
    
    for config in configs:
        folder_name = f"LightGBM_Entry_{config}"
        results_folder = os.path.join(folder_name, "results", "030_GOLD")
        performance_file = os.path.join(results_folder, "performance_summary.json")
        
        if os.path.exists(performance_file):
            try:
                import json
                with open(performance_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                found_files[config] = {
                    'found': True,
                    'win_rate': data.get('win_rate', 0),
                    'expectancy': data.get('expectancy', 0),
                    'num_trades': data.get('num_trades', 0),
                    'training_type': data.get('training_type', 'unknown'),
                    'file_path': performance_file
                }
                
                print(f"✅ {config}:")
                print(f"   📁 ไฟล์: {performance_file}")
                print(f"   📊 Win Rate: {data.get('win_rate', 0):.2%}")
                print(f"   💰 Expectancy: {data.get('expectancy', 0):.4f}")
                print(f"   🔢 Trades: {data.get('num_trades', 0)}")
                print(f"   🏗️ Type: {data.get('training_type', 'unknown')}")
                
            except Exception as e:
                found_files[config] = {'found': False, 'error': str(e)}
                print(f"❌ {config}: ข้อผิดพลาดในการอ่านไฟล์ - {e}")
        else:
            found_files[config] = {'found': False, 'error': 'File not found'}
            print(f"⚠️ {config}: ไม่พบไฟล์ผลลัพธ์")
            print(f"   📁 คาดหวัง: {performance_file}")
    
    # สรุปผลลัพธ์
    successful_configs = sum(1 for result in found_files.values() if result['found'])
    print(f"\n📋 สรุป: พบไฟล์ผลลัพธ์ {successful_configs}/{len(configs)} การตั้งค่า")
    
    return found_files

def test_comparison_system():
    """ทดสอบระบบเปรียบเทียบ"""
    print("\n🔍 ทดสอบระบบเปรียบเทียบ")
    print("=" * 50)
    
    try:
        # ลองรันการเปรียบเทียบ
        command = "python simple_entry_test.py check_results"
        print(f"🚀 รันคำสั่ง: {command}")
        
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ ระบบเปรียบเทียบทำงานสำเร็จ")
            print("📊 ผลลัพธ์:")
            for line in result.stdout.split('\n')[-10:]:  # แสดง 10 บรรทัดสุดท้าย
                if line.strip():
                    print(f"   {line}")
            return True
        else:
            print("❌ ระบบเปรียบเทียบมีปัญหา")
            print("📊 Error:")
            for line in result.stderr.split('\n')[:5]:  # แสดง 5 บรรทัดแรกของ error
                if line.strip():
                    print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ ระบบเปรียบเทียบใช้เวลานานเกินไป")
        return False
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการทดสอบระบบเปรียบเทียบ: {e}")
        return False

def main():
    print("🔧 Test Final Fix Script")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    print("📝 การแก้ไขที่ทำ:")
    print("   1. เพิ่มการบันทึกผลลัพธ์ในส่วน Multi-Model Architecture")
    print("   2. เพิ่มการคำนวณ trading stats จาก trade_df")
    print("   3. เพิ่ม metrics ใน result_dict สำหรับ Multi-Model")
    print("   4. แก้ไขการใช้ filtered_test_groups ในส่วน optimization")
    
    print("\n🚀 เริ่มการทดสอบ...")
    
    # ทดสอบการรันและบันทึกผลลัพธ์
    success = run_test_and_check_results()
    
    if success:
        print("\n🔍 ตรวจสอบไฟล์ที่บันทึก...")
        found_files = check_saved_files()
        
        print("\n🔍 ทดสอบระบบเปรียบเทียบ...")
        comparison_success = test_comparison_system()
        
        if comparison_success:
            print("\n🎉 การแก้ไขสำเร็จทั้งหมด!")
            print("✅ ระบบกรองข้อมูลทำงานถูกต้อง")
            print("✅ ระบบบันทึกผลลัพธ์ Multi-Model ทำงานถูกต้อง")
            print("✅ ระบบเปรียบเทียบทำงานถูกต้อง")
        else:
            print("\n⚠️ การแก้ไขบางส่วนยังไม่สมบูรณ์")
    else:
        print("\n❌ การแก้ไขยังไม่สำเร็จ")
    
    print(f"\n⏰ สิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
