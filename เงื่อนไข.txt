มีข้อที่ไม่แน่ใจ ลำดับการเขียนถูกต้องหรือไม่ และการกำหนดเงื่อนไข
<< คำถาม

1. คำถามเกี่ยวกับ MARKET_SCENARIOS
MARKET_SCENARIOS = {
    'trend_following': {
        'description': 'Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend)',
        'condition': lambda row: (
            # Buy signals ใน uptrend: Close > EMA200 และ Low > EMA200
            (row['Close'] > row['EMA200'] and row['Low'] > row['EMA200']) or << เงื่อนไข trend_following สำหรับ buy ?
            # Sell signals ใน downtrend: Close < EMA200 และ High < EMA200
            (row['Close'] < row['EMA200'] and row['High'] < row['EMA200']) << เงื่อนไข trend_following สำหรับ sell ?
        ),
        'actions': ['buy', 'sell'],  # รองรับทั้ง buy และ sell << ดูจากลำดับตรงนี้ ถูกมัย ?
        'type': 'trend_following',
        'strategy': 'momentum'
    },
    'counter_trend': {
        'description': 'Counter Trend Strategy (Sell ใน Uptrend + Buy ใน Downtrend)',
        'condition': lambda row: (
            # Buy signals ใน downtrend: Close < EMA200 และ High < EMA200 (mean reversion)
            (row['Close'] < row['EMA200'] and row['High'] < row['EMA200']) or << เงื่อนไข counter_trend สำหรับ buy ?
            # Sell signals ใน uptrend: Close > EMA200 และ Low > EMA200 (mean reversion)
            (row['Close'] > row['EMA200'] and row['Low'] > row['EMA200']) << เงื่อนไข counter_trend สำหรับ sell ?
        ),
        'actions': ['buy', 'sell'],  # รองรับทั้ง buy และ sell << ดูจากลำดับตรงนี้ ถูกมัย ?
        'type': 'counter_trend',
        'strategy': 'mean_reversion'
    }
}

2. คำถามเกี่ยวกับ entry_conditions 
entry_conditions = {
    # Scenario 1: Trend Following - มีทั้ง BUY และ SELL
    "trend_following": {
        "buy": lambda prev: ( << เงื่อนไข trend_following สำหรับ buy ?
            prev['close'] > prev['open'] and
            prev['close'] > prev['ema200'] and
            prev['rsi14'] > input_rsi_level_in * 1.0 and # rsi > 40
            prev['rsi14'] < input_rsi_level_over * 1.0 and # rsi < 70
            prev['macd_signal'] == 1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.5 and
            prev['pullback_buy'] > input_pull_back * 0.5 and
            prev['ratio_buy'] > input_take_profit
        ),
        "sell": lambda prev: ( << เงื่อนไข trend_following สำหรับ sell ?
            prev['close'] < prev['open'] and
            prev['close'] < prev['ema200'] and
            prev['rsi14'] < (100 - input_rsi_level_in) * 1.0 and # rsi < 100-40 = 60
            prev['rsi14'] > (100 - input_rsi_level_over) * 1.0 and # rsi > 100-70= 30
            prev['macd_signal'] == -1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.5 and
            prev['pullback_sell'] > input_pull_back * 0.5 and
            prev['ratio_sell'] > input_take_profit
        )
    },

    # Scenario 2: Counter Trend - มีทั้ง BUY และ SELL
    "counter_trend": {
        "buy": lambda prev: ( << เงื่อนไข counter_trend สำหรับ buy ?
            prev['close'] > prev['open'] and
            prev['close'] < prev['ema200'] and
            prev['rsi14'] > input_rsi_level_in * 1.0 and # rsi > 40
            prev['rsi14'] < input_rsi_level_over * 1.0 and # rsi < 70
            prev['macd_signal'] == 1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_buy'] > input_pull_back * 0.8 and
            prev['ratio_buy'] > input_take_profit
        ),
        "sell": lambda prev: ( << เงื่อนไข counter_trend สำหรับ sell ?
            prev['close'] < prev['open'] and
            prev['close'] > prev['ema200'] and
            prev['rsi14'] < (100 - input_rsi_level_in) * 1.0 and # rsi < 100-40 = 60
            prev['rsi14'] > (100 - input_rsi_level_over) * 1.0 and # rsi > 100-70= 30
            prev['macd_signal'] == -1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and
            prev['pullback_sell'] > input_pull_back * 0.8 and
            prev['ratio_sell'] > input_take_profit
        )
    }
}

3. การเรียกใช้ที่ create_trade_cycles_with_model()

กรณี buy
                        if close > ema200:
                            if 'trend_following' in entry_conditions:
                                tech_signal_buy = entry_conditions['trend_following']['buy'](prev_dict) << ใช้เงื่อนไข trend_following สำหรับ buy ถูกมัย
                            else:
                                tech_signal_buy = entry_condition_func['buy'](prev_dict) << ส่วนนี้เรียกอะไร หรือใช้ entry_conditions_legacy เงื่อนไข default
                        else:
                            if 'counter_trend' in entry_conditions:
                                tech_signal_buy = entry_conditions['counter_trend']['buy'](prev_dict) << ใช้เงื่อนไข counter_trend สำหรับ buy ถูกมัย
                            else:
                                tech_signal_buy = entry_condition_func['buy'](prev_dict) << ส่วนนี้เรียกอะไร หรือใช้ entry_conditions_legacy เงื่อนไข default

กรณี sell

                        if close < ema200:
                            if 'trend_following' in entry_conditions:
                                tech_signal_sell = entry_conditions['trend_following']['sell'](prev_dict) << ใช้เงื่อนไข trend_following สำหรับ sell ถูกมัย
                            else:
                                tech_signal_sell = entry_condition_func['sell'](prev_dict) << ส่วนนี้เรียกอะไร หรือใช้ entry_conditions_legacy เงื่อนไข default
                        else:
                            if 'counter_trend' in entry_conditions:
                                tech_signal_sell = entry_conditions['counter_trend']['sell'](prev_dict) << ใช้เงื่อนไข counter_trend สำหรับ sell ถูกมัย
                            else:
                                tech_signal_sell = entry_condition_func['sell'](prev_dict) << ส่วนนี้เรียกอะไร หรือใช้ entry_conditions_legacy เงื่อนไข default