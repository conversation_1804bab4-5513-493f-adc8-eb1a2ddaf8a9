#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Real Training Results Script
สคริปต์แก้ไขปัญหาการบันทึกผลลัพธ์จริงจากการเทรน
"""

import os
import json
import shutil
from datetime import datetime

def backup_test_files():
    """สำรองไฟล์ทดสอบ"""
    print("💾 สำรองไฟล์ทดสอบ")
    print("=" * 50)
    
    configs = [
        "config_1_macd_deep",
        "config_2_macd_signal", 
        "config_3_enhanced_deep",
        "config_4_enhanced_signal"
    ]
    
    backup_folder = f"Entry_Test_Backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_folder, exist_ok=True)
    
    for config in configs:
        folder_name = f"LightGBM_Entry_{config}"
        if os.path.exists(folder_name):
            backup_path = os.path.join(backup_folder, f"LightGBM_Entry_{config}")
            shutil.copytree(folder_name, backup_path)
            print(f"✅ สำรอง {folder_name} -> {backup_path}")
    
    print(f"📁 สำรองไฟล์ทดสอบที่: {backup_folder}")
    return backup_folder

def clear_test_files():
    """ลบไฟล์ทดสอบ"""
    print("\n🗑️ ลบไฟล์ทดสอบ")
    print("=" * 50)
    
    configs = [
        "config_1_macd_deep",
        "config_2_macd_signal", 
        "config_3_enhanced_deep",
        "config_4_enhanced_signal"
    ]
    
    for config in configs:
        folder_name = f"LightGBM_Entry_{config}"
        if os.path.exists(folder_name):
            shutil.rmtree(folder_name)
            print(f"🗑️ ลบ {folder_name}")
    
    # ลบโฟลเดอร์ Entry_Comparison_Results
    if os.path.exists("Entry_Comparison_Results"):
        shutil.rmtree("Entry_Comparison_Results")
        print(f"🗑️ ลบ Entry_Comparison_Results")

def check_debug_messages():
    """ตรวจสอบข้อความ debug ในการเทรนล่าสุด"""
    print("\n🔍 ตรวจสอบข้อความ debug")
    print("=" * 50)
    
    # ค้นหาไฟล์ log หรือ output ล่าสุด
    log_files = []
    for file in os.listdir('.'):
        if 'log' in file.lower() or 'output' in file.lower():
            log_files.append(file)
    
    if log_files:
        print(f"📁 พบไฟล์ log: {log_files}")
    else:
        print("❌ ไม่พบไฟล์ log")
    
    # ตรวจสอบว่ามีข้อความ debug ในการเทรนหรือไม่
    print("\n💡 ข้อความ debug ที่ควรเห็นในการเทรน:")
    print("   - '🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ (Multi-Model)'")
    print("   - '✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น (Multi-Model)'")
    print("   - '✅ บันทึกผลการประเมิน config_x_xxxxx สำหรับ GOLD M30'")

def create_training_guide():
    """สร้างคู่มือการเทรนที่ถูกต้อง"""
    print("\n📖 สร้างคู่มือการเทรนที่ถูกต้อง")
    print("=" * 50)
    
    guide_content = """# คู่มือการเทรนที่ถูกต้องสำหรับ Entry Config Comparison

## 🎯 ขั้นตอนการเทรนที่ถูกต้อง

### 1. ลบไฟล์ทดสอบ (ถ้ามี)
```bash
python fix_real_training_results.py
```

### 2. รันการเทรนจริง
```bash
# รันการเทรนด้วยการตั้งค่าแต่ละแบบ
python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30
```

### 3. ตรวจสอบข้อความ debug
ในระหว่างการเทรน ควรเห็นข้อความ:
- `🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ (Multi-Model)`
- `✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น (Multi-Model)`

### 4. ตรวจสอบไฟล์ที่สร้างขึ้น
```bash
python simple_entry_test.py check_results
```

## ⚠️ หากไม่เห็นข้อความ debug

1. ตรวจสอบว่าใช้ Multi-Model Architecture
2. ตรวจสอบว่าฟังก์ชัน save_entry_config_performance ถูกเรียกใช้
3. ตรวจสอบว่าไม่มี error ในการบันทึกไฟล์

## 🔧 การแก้ไขปัญหา

### ปัญหา: ไม่เห็นข้อความ debug
- ตรวจสอบว่าการแก้ไขในไฟล์ python_LightGBM_18.py ถูกบันทึกแล้ว
- ตรวจสอบว่าใช้ Multi-Model Architecture (USE_MULTI_MODEL_ARCHITECTURE = True)

### ปัญหา: ไฟล์ไม่ถูกสร้าง
- ตรวจสอบสิทธิ์การเขียนไฟล์
- ตรวจสอบว่าไม่มี error ในการสร้างโฟลเดอร์

### ปัญหา: ข้อมูลไม่ถูกต้อง
- ตรวจสอบการคำนวณ trading stats จาก trade_df
- ตรวจสอบว่า trade_df ไม่เป็น None หรือว่าง

---
สร้างเมื่อ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open('Training_Guide_Entry_Comparison.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ สร้างคู่มือที่: Training_Guide_Entry_Comparison.md")

def show_next_steps():
    """แสดงขั้นตอนต่อไป"""
    print("\n🚀 ขั้นตอนต่อไป")
    print("=" * 50)
    
    print("1. 🗑️ ลบไฟล์ทดสอบ:")
    print("   python fix_real_training_results.py")
    
    print("\n2. 🏃‍♂️ รันการเทรนจริง:")
    print("   python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30")
    
    print("\n3. 👀 ดูข้อความ debug ในระหว่างการเทรน:")
    print("   - ควรเห็น '🔍 Debug: กำลังเตรียมข้อมูล...'")
    print("   - ควรเห็น '✅ บันทึกผลลัพธ์การเปรียบเทียบ...'")
    
    print("\n4. ✅ ตรวจสอบผลลัพธ์:")
    print("   python simple_entry_test.py check_results")
    
    print("\n5. 📊 ดูคำแนะนำ:")
    print("   python entry_config_test.py --mode recommendations")
    
    print("\n💡 หมายเหตุ:")
    print("   - หากไม่เห็นข้อความ debug แสดงว่าการแก้ไขยังไม่ทำงาน")
    print("   - หากเห็นข้อความ debug แต่ไฟล์ไม่ถูกสร้าง ให้ตรวจสอบ error")
    print("   - การเทรนจริงจะใช้เวลานานกว่าไฟล์ทดสอบ")

def main():
    print("🔧 Fix Real Training Results Script")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    print("📝 สคริปต์นี้จะ:")
    print("   1. สำรองไฟล์ทดสอบที่มีอยู่")
    print("   2. ลบไฟล์ทดสอบเพื่อเตรียมการเทรนจริง")
    print("   3. สร้างคู่มือการเทรนที่ถูกต้อง")
    print("   4. แสดงขั้นตอนต่อไป")
    
    # ขอยืนยันจากผู้ใช้
    print("\n⚠️ คำเตือน: สคริปต์นี้จะลบไฟล์ทดสอบที่มีอยู่")
    response = input("ต้องการดำเนินการต่อหรือไม่? (y/N): ")
    
    if response.lower() in ['y', 'yes']:
        # สำรองไฟล์ทดสอบ
        backup_folder = backup_test_files()
        
        # ลบไฟล์ทดสอบ
        clear_test_files()
        
        # ตรวจสอบข้อความ debug
        check_debug_messages()
        
        # สร้างคู่มือ
        create_training_guide()
        
        # แสดงขั้นตอนต่อไป
        show_next_steps()
        
        print(f"\n✅ เตรียมการเสร็จสิ้น")
        print(f"💾 ไฟล์ทดสอบถูกสำรองที่: {backup_folder}")
        print(f"🚀 พร้อมสำหรับการเทรนจริง")
    else:
        print("❌ ยกเลิกการดำเนินการ")
        
        # แสดงขั้นตอนต่อไปแบบไม่ลบไฟล์
        print("\n💡 หากต้องการทดสอบระบบ:")
        print("   python simple_entry_test.py check_results")
        print("   python entry_config_test.py --mode recommendations")
    
    print(f"\n⏰ สิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
