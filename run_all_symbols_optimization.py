#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
รันการหาค่าที่เหมาะสมสำหรับทุก symbols และ timeframes
"""

import sys
import os
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def run_optimization_for_all_symbols():
    """รันการหาค่าที่เหมาะสมสำหรับทุก symbols"""
    print("🚀 รันการหาค่าที่เหมาะสมสำหรับทุก symbols")
    print("=" * 60)
    print(f"⏰ เวลาเริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        from python_LightGBM_17_Signal import run_multi_model_optimization
        
        # รายการ symbols และ timeframes
        symbols = ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'NZDUSD', 'USDCAD', 'USDJPY']
        timeframes = [30, 60]
        
        results = {}
        total_combinations = len(symbols) * len(timeframes)
        current_combination = 0
        
        print(f"📊 จำนวนการทดสอบทั้งหมด: {total_combinations} combinations")
        print()
        
        for symbol in symbols:
            results[symbol] = {}
            
            for timeframe in timeframes:
                current_combination += 1
                print(f"\n{'='*50}")
                print(f"🔍 [{current_combination}/{total_combinations}] {symbol} M{timeframe}")
                print(f"⏰ เวลา: {datetime.now().strftime('%H:%M:%S')}")
                
                try:
                    start_time = datetime.now()
                    result = run_multi_model_optimization(symbol, timeframe)
                    end_time = datetime.now()
                    
                    duration = (end_time - start_time).total_seconds()
                    
                    if result:
                        # ตรวจสอบความแตกต่าง
                        thresholds = list(result['optimal_thresholds'].values())
                        nbars_values = list(result['optimal_nbars'].values())
                        
                        threshold_diff = len(set(thresholds)) > 1
                        nbars_diff = len(set(nbars_values)) > 1
                        
                        results[symbol][timeframe] = {
                            'success': True,
                            'duration': duration,
                            'thresholds': thresholds,
                            'nbars': nbars_values,
                            'threshold_different': threshold_diff,
                            'nbars_different': nbars_diff,
                            'scenarios': result['scenarios']
                        }
                        
                        print(f"✅ สำเร็จ ({duration:.1f}s)")
                        print(f"   Thresholds: {thresholds} ({'✅ แตกต่าง' if threshold_diff else '❌ เหมือนกัน'})")
                        print(f"   nBars_SL: {nbars_values} ({'✅ แตกต่าง' if nbars_diff else '❌ เหมือนกัน'})")
                        
                    else:
                        results[symbol][timeframe] = {
                            'success': False,
                            'duration': duration,
                            'error': 'Optimization failed'
                        }
                        print(f"❌ ไม่สำเร็จ ({duration:.1f}s)")
                        
                except Exception as e:
                    results[symbol][timeframe] = {
                        'success': False,
                        'duration': 0,
                        'error': str(e)
                    }
                    print(f"❌ เกิดข้อผิดพลาด: {e}")
        
        # สรุปผลลัพธ์
        print(f"\n{'='*60}")
        print("📊 สรุปผลลัพธ์:")
        
        successful = 0
        threshold_different_count = 0
        nbars_different_count = 0
        
        for symbol in symbols:
            print(f"\n💰 {symbol}:")
            for timeframe in timeframes:
                result = results[symbol][timeframe]
                if result['success']:
                    successful += 1
                    status = "✅"
                    
                    if result['threshold_different']:
                        threshold_different_count += 1
                    if result['nbars_different']:
                        nbars_different_count += 1
                    
                    print(f"  🕐 M{timeframe}: {status} ({result['duration']:.1f}s)")
                    print(f"     Thresholds: {result['thresholds']}")
                    print(f"     nBars_SL: {result['nbars']}")
                    print(f"     แตกต่าง: T={'✅' if result['threshold_different'] else '❌'}, N={'✅' if result['nbars_different'] else '❌'}")
                else:
                    print(f"  🕐 M{timeframe}: ❌ {result.get('error', 'Unknown error')}")
        
        print(f"\n🎯 สถิติรวม:")
        print(f"   สำเร็จ: {successful}/{total_combinations} ({successful/total_combinations*100:.1f}%)")
        print(f"   Threshold แตกต่าง: {threshold_different_count}/{successful} ({threshold_different_count/successful*100:.1f}%)")
        print(f"   nBars_SL แตกต่าง: {nbars_different_count}/{successful} ({nbars_different_count/successful*100:.1f}%)")
        
        # บันทึกผลลัพธ์
        import json
        summary_file = f"LightGBM_Multi/training_summaries/optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs("LightGBM_Multi/training_summaries", exist_ok=True)
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 บันทึกผลลัพธ์: {summary_file}")
        
        print(f"\n⏰ เวลาสิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if successful == total_combinations and threshold_different_count > 0:
            print("\n🎉 การแก้ไขสำเร็จสมบูรณ์!")
            print("💡 ระบบสามารถสร้างค่าพารามิเตอร์ที่แตกต่างกันได้แล้ว")
        elif successful >= total_combinations * 0.8:
            print("\n✅ การแก้ไขส่วนใหญ่สำเร็จ")
        else:
            print("\n⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        
        return results
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการรัน: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    run_optimization_for_all_symbols()
