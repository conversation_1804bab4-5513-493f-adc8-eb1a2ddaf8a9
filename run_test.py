#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Run Test Script
สคริปต์รันการทดสอบจริงและแสดงผลลัพธ์
"""

import subprocess
import sys
import time
from datetime import datetime

def run_command(command, timeout=300):
    """รันคำสั่งและแสดงผลลัพธ์"""
    print(f"🚀 รันคำสั่ง: {command}")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 80)
    
    try:
        # รันคำสั่งและจับ output
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        output_lines = []
        start_time = time.time()
        
        # อ่าน output แบบ real-time
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                print(line)
                output_lines.append(line)
                
                # ตรวจสอบ timeout
                if time.time() - start_time > timeout:
                    print(f"\n⏰ Timeout หลังจาก {timeout} วินาที")
                    process.terminate()
                    break
                
                # หยุดเมื่อเห็นข้อความเฉพาะ (เพื่อประหยัดเวลา)
                if "📊 ผลการกรอง:" in line:
                    print(f"\n✅ พบข้อความการกรอง - หยุดการทดสอบ")
                    process.terminate()
                    break
                    
                if "รวมการเทรนทั้งหมด:" in line:
                    print(f"\n✅ พบข้อความจำนวนรอบการเทรน - หยุดการทดสอบ")
                    process.terminate()
                    break
        
        return_code = process.poll()
        
        print("=" * 80)
        print(f"⏰ สิ้นสุด: {datetime.now().strftime('%H:%M:%S')}")
        print(f"📊 Return code: {return_code}")
        
        return output_lines, return_code
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")
        return [], -1

def analyze_output(output_lines):
    """วิเคราะห์ผลลัพธ์"""
    print(f"\n🔍 วิเคราะห์ผลลัพธ์:")
    print("=" * 50)
    
    # ค้นหาข้อความสำคัญ
    important_lines = []
    
    for line in output_lines:
        if any(keyword in line for keyword in [
            "🎯 กรองข้อมูล:",
            "📈 Symbols:",
            "⏰ Timeframes:",
            "⏭️ ข้าม",
            "✅ รวม",
            "📊 ผลการกรอง:",
            "รวมการเทรนทั้งหมด:",
            "DEBUG"
        ]):
            important_lines.append(line)
    
    if important_lines:
        print("📋 ข้อความสำคัญ:")
        for line in important_lines:
            print(f"   {line}")
    else:
        print("❌ ไม่พบข้อความสำคัญ")
    
    # ตรวจสอบการกรอง
    filter_found = any("🎯 กรองข้อมูล:" in line for line in output_lines)
    skip_found = any("⏭️ ข้าม" in line for line in output_lines)
    include_found = any("✅ รวม" in line for line in output_lines)
    
    print(f"\n📊 สรุปการตรวจสอบ:")
    print(f"   การกรองข้อมูล: {'✅ พบ' if filter_found else '❌ ไม่พบ'}")
    print(f"   การข้ามไฟล์: {'✅ พบ' if skip_found else '❌ ไม่พบ'}")
    print(f"   การรวมไฟล์: {'✅ พบ' if include_found else '❌ ไม่พบ'}")
    
    if filter_found and skip_found and include_found:
        print("🎉 การกรองข้อมูลทำงานถูกต้อง!")
    else:
        print("⚠️ การกรองข้อมูลอาจไม่ทำงานถูกต้อง")

def test_quick():
    """ทดสอบด้วยสคริปต์ test_quick.py"""
    print("🧪 ทดสอบด้วย test_quick.py")
    print("=" * 50)
    
    output_lines, return_code = run_command("python test_quick.py", timeout=60)
    
    if return_code == 0:
        print("✅ test_quick.py ทำงานสำเร็จ")
    else:
        print("❌ test_quick.py มีปัญหา")
    
    return output_lines

def test_entry_config():
    """ทดสอบด้วย entry_config_test.py"""
    print("\n🧪 ทดสอบด้วย entry_config_test.py")
    print("=" * 50)
    
    command = "python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30"
    output_lines, return_code = run_command(command, timeout=120)
    
    analyze_output(output_lines)
    
    return output_lines

def test_debug():
    """ทดสอบด้วย debug_filter.py"""
    print("\n🧪 ทดสอบด้วย debug_filter.py")
    print("=" * 50)
    
    output_lines, return_code = run_command("python debug_filter.py", timeout=60)
    
    if return_code == 0:
        print("✅ debug_filter.py ทำงานสำเร็จ")
    else:
        print("❌ debug_filter.py มีปัญหา")
    
    return output_lines

def main():
    print("🧪 Run Test Script")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # ทดสอบตามลำดับ
    tests = [
        ("Quick Test", test_quick),
        ("Debug Test", test_debug),
        ("Entry Config Test", test_entry_config)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            output = test_func()
            results[test_name] = {"status": "success", "output": output}
        except KeyboardInterrupt:
            print(f"\n⏹️ หยุดการทดสอบ {test_name} ด้วย Ctrl+C")
            results[test_name] = {"status": "interrupted", "output": []}
            break
        except Exception as e:
            print(f"\n❌ ข้อผิดพลาดในการทดสอบ {test_name}: {e}")
            results[test_name] = {"status": "error", "output": []}
    
    # สรุปผลลัพธ์
    print(f"\n{'='*20} สรุปผลลัพธ์ {'='*20}")
    for test_name, result in results.items():
        status_icon = {"success": "✅", "error": "❌", "interrupted": "⏹️"}.get(result["status"], "❓")
        print(f"{status_icon} {test_name}: {result['status']}")
    
    print(f"\n⏰ สิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
