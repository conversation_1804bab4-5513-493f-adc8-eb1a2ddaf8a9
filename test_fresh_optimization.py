#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการหาค่าที่เหมาะสมใหม่หลังจากลบไฟล์เก่า
"""

import sys
import os
import glob
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def clean_old_parameter_files():
    """ลบไฟล์พารามิเตอร์เก่า"""
    print("🧹 ลบไฟล์พารามิเตอร์เก่า")
    print("=" * 50)
    
    # ลบไฟล์ threshold และ nBars_SL เก่า
    threshold_files = glob.glob("LightGBM_Multi/thresholds/*_optimal_threshold.pkl")
    nbars_files = glob.glob("LightGBM_Multi/thresholds/*_optimal_nBars_SL.pkl")
    
    deleted_count = 0
    
    for file_path in threshold_files + nbars_files:
        try:
            os.remove(file_path)
            print(f"🗑️ ลบ: {file_path}")
            deleted_count += 1
        except Exception as e:
            print(f"❌ ไม่สามารถลบ {file_path}: {e}")
    
    print(f"✅ ลบไฟล์เสร็จสิ้น: {deleted_count} ไฟล์")
    return deleted_count > 0

def test_default_values():
    """ทดสอบค่า default ที่แตกต่างกันตาม scenario"""
    print("\n🎯 ทดสอบค่า default ที่แตกต่างกันตาม scenario")
    print("=" * 60)
    
    try:
        from python_LightGBM_17_Signal import (
            get_default_threshold_by_scenario,
            get_default_nbars_by_scenario
        )
        
        symbols = ['GOLD', 'AUDUSD', 'EURUSD', 'USDJPY']
        timeframes = [30, 60]
        scenarios = ['trend_following', 'counter_trend']
        
        print("📊 ค่า Default Threshold และ nBars_SL:")
        print()
        
        different_thresholds = False
        different_nbars = False
        
        for symbol in symbols:
            print(f"💰 {symbol}:")
            for timeframe in timeframes:
                print(f"  🕐 M{timeframe}:")
                
                threshold_values = []
                nbars_values = []
                
                for scenario in scenarios:
                    threshold = get_default_threshold_by_scenario(scenario, symbol)
                    nbars = get_default_nbars_by_scenario(scenario, symbol, timeframe)
                    
                    threshold_values.append(threshold)
                    nbars_values.append(nbars)
                    
                    print(f"    📊 {scenario}: threshold={threshold:.3f}, nBars_SL={nbars}")
                
                # ตรวจสอบความแตกต่าง
                if len(set(threshold_values)) > 1:
                    different_thresholds = True
                if len(set(nbars_values)) > 1:
                    different_nbars = True
                
                print()
        
        print("🔍 การวิเคราะห์ความแตกต่าง:")
        print(f"   Threshold แตกต่างกัน: {'✅ ใช่' if different_thresholds else '❌ ไม่'}")
        print(f"   nBars_SL แตกต่างกัน: {'✅ ใช่' if different_nbars else '❌ ไม่'}")
        
        return different_thresholds and different_nbars
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบค่า default: {e}")
        return False

def test_data_separation():
    """ทดสอบการแยกข้อมูลตาม scenario"""
    print("\n📊 ทดสอบการแยกข้อมูลตาม scenario")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import (
            filter_data_by_scenario,
            load_validation_data_for_optimization
        )
        
        # ทดสอบกับ GOLD M30
        symbol = "GOLD"
        timeframe = 30
        
        print(f"🔍 โหลดข้อมูล validation สำหรับ {symbol} M{timeframe}")
        val_df = load_validation_data_for_optimization(symbol, timeframe)
        
        if val_df is None:
            print("❌ ไม่สามารถโหลดข้อมูลได้")
            return False
        
        print(f"✅ โหลดข้อมูลสำเร็จ: {val_df.shape}")
        
        # ทดสอบการแยกข้อมูล
        scenarios = ['trend_following', 'counter_trend']
        filtered_data = {}
        
        for scenario in scenarios:
            filtered_df = filter_data_by_scenario(val_df, scenario)
            filtered_data[scenario] = len(filtered_df)
            print(f"📊 {scenario}: {len(filtered_df)}/{len(val_df)} samples ({len(filtered_df)/len(val_df)*100:.1f}%)")
        
        # ตรวจสอบว่าข้อมูลแตกต่างกัน
        data_different = filtered_data['trend_following'] != filtered_data['counter_trend']
        
        print(f"\n🔍 การวิเคราะห์:")
        print(f"   ข้อมูลแตกต่างกัน: {'✅ ใช่' if data_different else '❌ ไม่'}")
        print(f"   Trend Following: {filtered_data['trend_following']} samples")
        print(f"   Counter Trend: {filtered_data['counter_trend']} samples")
        
        return data_different
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบการแยกข้อมูล: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fresh_optimization():
    """ทดสอบการหาค่าที่เหมาะสมใหม่"""
    print("\n🚀 ทดสอบการหาค่าที่เหมาะสมใหม่")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import run_multi_model_optimization
        
        # ทดสอบกับ GOLD M30
        symbol = "GOLD"
        timeframe = 30
        
        print(f"🔍 กำลังหา optimal parameters สำหรับ {symbol} M{timeframe}")
        print("⚠️ การทดสอบนี้อาจใช้เวลา 2-3 นาที...")
        
        start_time = datetime.now()
        result = run_multi_model_optimization(symbol, timeframe)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        print(f"\n⏱️ เวลาที่ใช้: {duration:.1f} วินาที")
        
        if result:
            print("\n✅ การหาค่าที่เหมาะสมสำเร็จ!")
            print(f"📊 ผลลัพธ์สำหรับ {symbol} M{timeframe}:")
            
            for scenario in result['scenarios']:
                threshold = result['optimal_thresholds'].get(scenario, 'N/A')
                nbars = result['optimal_nbars'].get(scenario, 'N/A')
                print(f"   {scenario}: threshold={threshold}, nBars_SL={nbars}")
            
            # ตรวจสอบว่าค่าแตกต่างกันหรือไม่
            thresholds = list(result['optimal_thresholds'].values())
            nbars_values = list(result['optimal_nbars'].values())
            
            threshold_diff = len(set(thresholds)) > 1
            nbars_diff = len(set(nbars_values)) > 1
            
            print(f"\n🔍 การวิเคราะห์ความแตกต่าง:")
            print(f"   Threshold values: {thresholds}")
            print(f"   nBars_SL values: {nbars_values}")
            print(f"   Threshold แตกต่างกัน: {'✅ ใช่' if threshold_diff else '❌ ไม่'}")
            print(f"   nBars_SL แตกต่างกัน: {'✅ ใช่' if nbars_diff else '❌ ไม่'}")
            
            success = threshold_diff or nbars_diff
            
            if success:
                print("\n🎉 สำเร็จ! ค่าพารามิเตอร์แตกต่างกันระหว่าง scenarios แล้ว")
            else:
                print("\n⚠️ ค่าพารามิเตอร์ยังคงเหมือนกัน")
                print("💡 แนวทางเพิ่มเติม:")
                print("   1. ปรับเงื่อนไขการกรองข้อมูลให้เข้มงวดขึ้น")
                print("   2. ใช้ threshold range ที่กว้างขึ้น")
                print("   3. เพิ่มข้อมูล features ที่ขาดหายไป")
            
            return success
        else:
            print("❌ การหาค่าที่เหมาะสมไม่สำเร็จ")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ optimization: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔄 การทดสอบการหาค่าที่เหมาะสมใหม่หลังจากลบไฟล์เก่า")
    print("=" * 80)
    print(f"⏰ เวลาเริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # ขั้นตอนการทดสอบ
    steps = [
        ("ลบไฟล์พารามิเตอร์เก่า", clean_old_parameter_files),
        ("ทดสอบค่า default", test_default_values),
        ("ทดสอบการแยกข้อมูล", test_data_separation),
        ("ทดสอบการหาค่าที่เหมาะสมใหม่", test_fresh_optimization),
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            result = step_func()
            results.append((step_name, result))
            status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
            print(f"\n{status} {step_name}")
        except Exception as e:
            print(f"\n❌ {step_name}: เกิดข้อผิดพลาด - {e}")
            results.append((step_name, False))
    
    # สรุปผลการทดสอบ
    print(f"\n{'='*80}")
    print("📊 สรุปผลการทดสอบ:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for step_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
        print(f"   {status} {step_name}")
    
    print(f"\n🎯 ผลรวม: {passed}/{total} ขั้นตอนผ่าน")
    print(f"⏰ เวลาสิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("\n🎉 การทดสอบทั้งหมดผ่าน! ปัญหาได้รับการแก้ไขแล้ว")
        print("\n📋 ขั้นตอนถัดไป:")
        print("   1. รันการหาค่าที่เหมาะสมสำหรับ symbols อื่นๆ")
        print("   2. อัปเดต MT5 WebRequest Server")
        print("   3. ทดสอบการเทรดจริง")
    elif passed >= 3:
        print("\n✅ การแก้ไขส่วนใหญ่สำเร็จ แต่ยังมีจุดที่ต้องปรับปรุง")
        print("\n💡 แนวทางเพิ่มเติม:")
        print("   1. ปรับปรุงอัลกอริทึมการหาค่าที่เหมาะสม")
        print("   2. เพิ่มข้อมูล features ที่ขาดหายไป")
        print("   3. ปรับเงื่อนไขการกรองข้อมูล")
    else:
        print("\n⚠️ ยังมีปัญหาที่ต้องแก้ไข กรุณาตรวจสอบและปรับปรุงเพิ่มเติม")

if __name__ == "__main__":
    main()
