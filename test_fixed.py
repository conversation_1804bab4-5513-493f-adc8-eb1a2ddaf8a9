#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Fixed Script
สคริปต์ทดสอบหลังจากแก้ไขปัญหาการกรองข้อมูล
"""

import subprocess
import sys
import time
from datetime import datetime

def run_test_command():
    """รันคำสั่งทดสอบและตรวจสอบผลลัพธ์"""
    print("🧪 ทดสอบคำสั่งหลังแก้ไข")
    print("=" * 50)
    
    command = "python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30"
    print(f"🚀 รันคำสั่ง: {command}")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 80)
    
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        output_lines = []
        start_time = time.time()
        
        # ตัวแปรสำหรับตรวจสอบ
        filter_working = False
        optimization_fixed = False
        training_count = 0
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                print(line)
                output_lines.append(line)
                
                # ตรวจสอบการกรองข้อมูล
                if "📊 ผลการกรอง: 1 กลุ่ม, รวม 1 ไฟล์" in line:
                    filter_working = True
                    print("\n✅ การกรองข้อมูลทำงานถูกต้อง!")
                
                # ตรวจสอบ optimization ที่แก้ไขแล้ว
                if "🔍 DEBUG: ใช้ filtered_test_groups แทน test_groups" in line:
                    optimization_fixed = True
                    print("\n✅ การแก้ไข optimization ทำงานถูกต้อง!")
                
                # นับจำนวนการเทรน
                if "กำลังทดสอบไฟล์:" in line:
                    training_count += 1
                
                # หยุดเมื่อเห็นข้อความที่แสดงว่าการแก้ไขทำงาน
                if optimization_fixed and training_count >= 2:
                    print(f"\n✅ ทดสอบเสร็จสิ้น - การแก้ไขทำงานถูกต้อง")
                    process.terminate()
                    break
                
                # หยุดหากใช้เวลานานเกินไป
                if time.time() - start_time > 300:  # 5 นาที
                    print(f"\n⏰ Timeout หลังจาก 5 นาที")
                    process.terminate()
                    break
        
        # วิเคราะห์ผลลัพธ์
        print("\n" + "=" * 80)
        print("📊 สรุปผลการทดสอบ:")
        print(f"   การกรองข้อมูล: {'✅ ทำงานถูกต้อง' if filter_working else '❌ ไม่ทำงาน'}")
        print(f"   การแก้ไข optimization: {'✅ ทำงานถูกต้อง' if optimization_fixed else '❌ ไม่ทำงาน'}")
        print(f"   จำนวนการเทรน: {training_count} ครั้ง")
        
        if filter_working and optimization_fixed:
            print("\n🎉 การแก้ไขสำเร็จ! ระบบกรองข้อมูลทำงานถูกต้องแล้ว")
            return True
        else:
            print("\n⚠️ ยังมีปัญหาที่ต้องแก้ไข")
            return False
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")
        return False

def check_key_messages(output_lines):
    """ตรวจสอบข้อความสำคัญ"""
    print("\n🔍 ตรวจสอบข้อความสำคัญ:")
    print("-" * 50)
    
    key_messages = [
        ("การกรองข้อมูล", "🎯 กรองข้อมูล:"),
        ("การข้ามไฟล์", "⏭️ ข้าม"),
        ("การรวมไฟล์", "✅ รวม GOLD_M30_FIXED.csv"),
        ("ผลการกรอง", "📊 ผลการกรอง: 1 กลุ่ม, รวม 1 ไฟล์"),
        ("จำนวนรอบ", "📈 รวมการเทรนทั้งหมด: 2 รอบ"),
        ("DEBUG optimization", "🔍 DEBUG: ใช้ filtered_test_groups"),
        ("จำนวนไฟล์ optimization", "จำนวนไฟล์ทั้งหมด: 1")
    ]
    
    results = {}
    for name, pattern in key_messages:
        found = any(pattern in line for line in output_lines)
        results[name] = found
        status = "✅" if found else "❌"
        print(f"   {status} {name}: {'พบ' if found else 'ไม่พบ'}")
    
    return results

def main():
    print("🔧 Test Fixed Script")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    print("📝 การแก้ไขที่ทำ:")
    print("   1. เพิ่มพารามิเตอร์ filter_symbols และ filter_timeframes ใน run_main_analysis()")
    print("   2. เพิ่มตรรกะการกรองข้อมูลใน run_main_analysis()")
    print("   3. แก้ไขการใช้ test_groups เป็น filtered_test_groups ในส่วน optimization")
    print("   4. เพิ่ม debug ข้อมูลเพื่อตรวจสอบการทำงาน")
    
    print("\n🚀 เริ่มการทดสอบ...")
    
    success = run_test_command()
    
    print(f"\n⏰ สิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("\n🎯 ขั้นตอนต่อไป:")
        print("   1. ทดสอบกับ symbols และ timeframes อื่นๆ")
        print("   2. รันการเปรียบเทียบแบบเต็ม")
        print("   3. ตรวจสอบผลลัพธ์การเปรียบเทียบ")
    else:
        print("\n🔧 ต้องแก้ไขเพิ่มเติม:")
        print("   1. ตรวจสอบข้อความ error ที่แสดง")
        print("   2. ตรวจสอบการส่งพารามิเตอร์")
        print("   3. ตรวจสอบตรรกะการกรอง")

if __name__ == "__main__":
    main()
