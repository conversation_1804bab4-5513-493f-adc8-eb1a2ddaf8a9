#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Entry Config Test Script
สคริปต์ทดสอบง่ายๆ สำหรับระบบเปรียบเทียบ Entry Conditions

วิธีใช้งาน:
1. python simple_entry_test.py test_single    # ทดสอบการตั้งค่าเดียว
2. python simple_entry_test.py test_all       # ทดสอบทั้งหมด
3. python simple_entry_test.py check_results  # ตรวจสอบผลลัพธ์
"""

import sys
import os
import json
from datetime import datetime

def test_single_config():
    """ทดสอบการตั้งค่าเดียว"""
    print("🔧 ทดสอบการตั้งค่าเดียว")
    print("=" * 50)
    
    # เปลี่ยนการตั้งค่าใน python_LightGBM_18.py
    config_to_test = "config_1_macd_deep"
    
    print(f"📝 การตั้งค่าที่จะทดสอบ: {config_to_test}")
    print(f"📈 Symbol: GOLD, Timeframe: M30")
    
    # แก้ไขไฟล์ python_LightGBM_18.py
    try:
        with open('python_LightGBM_18.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # เปลี่ยน CURRENT_ENTRY_CONFIG
        old_line = 'CURRENT_ENTRY_CONFIG = "config_3_enhanced_deep"'
        new_line = f'CURRENT_ENTRY_CONFIG = "{config_to_test}"'
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            with open('python_LightGBM_18.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ เปลี่ยนการตั้งค่าเป็น {config_to_test}")
        else:
            print(f"⚠️ ไม่พบบรรทัดที่ต้องแก้ไข")
            
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการแก้ไขไฟล์: {e}")
        return False
    
    print(f"\n🚀 รันการเทรน...")
    print(f"💡 คำแนะนำ: รันคำสั่ง python python_LightGBM_18.py")
    
    return True

def test_all_configs():
    """ทดสอบการตั้งค่าทั้งหมด"""
    print("🔧 ทดสอบการตั้งค่าทั้งหมด")
    print("=" * 50)
    
    configs = [
        "config_1_macd_deep",
        "config_2_macd_signal", 
        "config_3_enhanced_deep",
        "config_4_enhanced_signal"
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"\n📝 ขั้นตอนที่ {i}: ทดสอบ {config}")
        
        # เปลี่ยนการตั้งค่า
        try:
            with open('python_LightGBM_18.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # หาบรรทัดที่มี CURRENT_ENTRY_CONFIG
            lines = content.split('\n')
            for j, line in enumerate(lines):
                if 'CURRENT_ENTRY_CONFIG = ' in line and not line.strip().startswith('#'):
                    lines[j] = f'CURRENT_ENTRY_CONFIG = "{config}"  # เปลี่ยนโดย simple_entry_test.py'
                    break
            
            content = '\n'.join(lines)
            
            with open('python_LightGBM_18.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"   ✅ เปลี่ยนการตั้งค่าเป็น {config}")
            print(f"   💡 รันคำสั่ง: python python_LightGBM_18.py")
            print(f"   ⏳ รอให้การเทรนเสร็จ แล้วกด Enter เพื่อไปขั้นตอนต่อไป...")
            
            if i < len(configs):
                input()  # รอให้ผู้ใช้กด Enter
                
        except Exception as e:
            print(f"   ❌ ข้อผิดพลาด: {e}")
    
    print(f"\n✅ เสร็จสิ้นการตั้งค่าทั้งหมด")
    print(f"💡 ตอนนี้สามารถรันคำสั่ง check_results เพื่อดูผลลัพธ์")

def check_results():
    """ตรวจสอบผลลัพธ์"""
    print("📊 ตรวจสอบผลลัพธ์")
    print("=" * 50)
    
    configs = [
        "config_1_macd_deep",
        "config_2_macd_signal", 
        "config_3_enhanced_deep",
        "config_4_enhanced_signal"
    ]
    
    symbols = ["GOLD"]
    timeframes = [30, 60]
    
    results_found = {}
    
    for config in configs:
        results_found[config] = {}
        
        for symbol in symbols:
            for timeframe in timeframes:
                # ตรวจสอบโฟลเดอร์ผลลัพธ์
                folder_name = f"LightGBM_Entry_{config}"
                results_folder = os.path.join(folder_name, "results", f"{timeframe:03d}_{symbol}")
                performance_file = os.path.join(results_folder, "performance_summary.json")
                
                key = f"{symbol}_M{timeframe}"
                
                if os.path.exists(performance_file):
                    try:
                        with open(performance_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        results_found[config][key] = {
                            'found': True,
                            'win_rate': data.get('win_rate', 0),
                            'expectancy': data.get('expectancy', 0),
                            'num_trades': data.get('num_trades', 0),
                            'file_path': performance_file
                        }
                        
                        print(f"✅ {config} - {key}: พบผลลัพธ์")
                        print(f"   📊 Win Rate: {data.get('win_rate', 0):.2%}")
                        print(f"   💰 Expectancy: {data.get('expectancy', 0):.4f}")
                        print(f"   🔢 Trades: {data.get('num_trades', 0)}")
                        
                    except Exception as e:
                        results_found[config][key] = {'found': False, 'error': str(e)}
                        print(f"❌ {config} - {key}: ข้อผิดพลาดในการอ่านไฟล์ - {e}")
                else:
                    results_found[config][key] = {'found': False, 'error': 'File not found'}
                    print(f"⚠️ {config} - {key}: ไม่พบไฟล์ผลลัพธ์")
                    print(f"   📁 คาดหวัง: {performance_file}")
    
    # สรุปผลลัพธ์
    print(f"\n📋 สรุปผลลัพธ์:")
    print("-" * 50)
    
    for symbol in symbols:
        for timeframe in timeframes:
            key = f"{symbol}_M{timeframe}"
            print(f"\n🎯 {key}:")
            
            best_config = None
            best_score = 0
            
            for config in configs:
                if key in results_found[config] and results_found[config][key]['found']:
                    result = results_found[config][key]
                    
                    # คำนวณคะแนนง่ายๆ
                    score = (result['win_rate'] * 0.5 + 
                            min(result['expectancy'], 1.0) * 0.3 + 
                            min(result['num_trades'] / 50, 1.0) * 0.2)
                    
                    print(f"   {config}: Score {score:.3f}")
                    
                    if score > best_score:
                        best_score = score
                        best_config = config
                else:
                    print(f"   {config}: ไม่มีข้อมูล")
            
            if best_config:
                print(f"   🏆 แนะนำ: {best_config} (Score: {best_score:.3f})")
            else:
                print(f"   ❌ ไม่มีข้อมูลเพียงพอ")
    
    # บันทึกผลลัพธ์
    summary_file = f"entry_comparison_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(results_found, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 บันทึกสรุปผลลัพธ์ที่: {summary_file}")

def show_usage():
    """แสดงวิธีการใช้งาน"""
    print("📖 วิธีการใช้งาน Simple Entry Test")
    print("=" * 50)
    print("1. ทดสอบการตั้งค่าเดียว:")
    print("   python simple_entry_test.py test_single")
    print("")
    print("2. ทดสอบการตั้งค่าทั้งหมด:")
    print("   python simple_entry_test.py test_all")
    print("")
    print("3. ตรวจสอบผลลัพธ์:")
    print("   python simple_entry_test.py check_results")
    print("")
    print("📝 ขั้นตอนที่แนะนำ:")
    print("   1. รัน test_single เพื่อทดสอบระบบ")
    print("   2. รัน test_all เพื่อทดสอบทั้งหมด")
    print("   3. รัน check_results เพื่อดูผลลัพธ์")

def main():
    if len(sys.argv) < 2:
        show_usage()
        return
    
    command = sys.argv[1]
    
    print(f"🚀 Simple Entry Config Test")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    if command == "test_single":
        test_single_config()
    elif command == "test_all":
        test_all_configs()
    elif command == "check_results":
        check_results()
    else:
        print(f"❌ คำสั่งไม่ถูกต้อง: {command}")
        show_usage()

if __name__ == "__main__":
    main()
