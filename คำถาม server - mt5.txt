ต้องการให้มีการตรวจสอบเพิ่มเติม

JSON ถูกตัดทอน
JSON ไม่สมบูรณ์!

ให้มีการแสดง print ความยาวข้อความเพื่อตรวจสอบจำนวน
python serverส่งข้อความไปเท่าไหร่
mt5 รับข้อความได้เท่าไหร่
จำนวนทั้ง 2 ต้องเท่ากัน

ยังไม่มีการเปิด ตรวจสอบอย่างละเอียดอีกครั้งที่ mt5

// mt5

2025.07.24 14:06:57.075	mt5_to_python_10_Multimodel (GOLD#,M30)	[2025.07.24 14:06:56] SYSTEM: SYSTEM_INIT: STARTING Symbol=GOLD#, Timeframe=PERIOD_M30, URL=http://127.0.0.1:54321/data
2025.07.24 14:06:57.075	mt5_to_python_10_Multimodel (GOLD#,M30)	Initializing HttpRequestSender for GOLD# PERIOD_M30
2025.07.24 14:06:57.075	mt5_to_python_10_Multimodel (GOLD#,M30)	Sending data to URL: http://127.0.0.1:54321/data
2025.07.24 14:06:57.075	mt5_to_python_10_Multimodel (GOLD#,M30)	symbol GOLD# ACContract 100.0 spread 25 slippage 5 pv 1.0
2025.07.24 14:06:57.075	mt5_to_python_10_Multimodel (GOLD#,M30)	Symbol GOLD# Base GOLD Quote USD Timeframe PERIOD_M30 : Version 1.0 Magic 13098
2025.07.24 14:06:57.075	mt5_to_python_10_Multimodel (GOLD#,M30)	Successfully copied 210 historical bars for initial batch send (GOLD# PERIOD_M30).
2025.07.24 14:06:57.076	mt5_to_python_10_Multimodel (GOLD#,M30)	Sending initial batch of 210 historical bars...
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	Web Request successful! Status 200.
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	Received Response Body: {"action_type":"sell","analysis_summary":{"counter_trend":{"buy":{"confidence":0.0,"model_info":{"action":"buy","available":true,"scenario":"counter_trend"},"should_trade":false},"sell":{"confidence":0.9552734381534381,"model_info":{"action":"sell","available":true,"scenario":"counter_trend"},"should_trade":true}},"trend_following":{"buy":{"confidence":0.0,"model_info":{"action":"buy","available":true,"scenario":"trend_following"},"should_trade":false},"sell":{"confidence":0.9553426
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	🔍 DEBUG: Parsing confidence...
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   JSON length: 1488
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   analysis_summary_start: 22
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   search_limit: 22
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   ⚠️ Found confidence inside analysis_summary, searching for main confidence...
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   📊 Final parsed confidence: 0.0000
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	Parse : Symbol GOLD Period PERIOD_M30 spread 25.00
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	Parse : confidence 0.0000 signal SELL class SELL
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	🔍 DEBUG: Parsing scenario_used...
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   scenario_used_key_start: 960
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   parsed scenario_used: 'Trend-Following'
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	GOLD# (non-string passed): Parsed Response - Signal: 'SELL', Confidence: 0.0000, SL: 3371.61000, TP: 3342.26000, Response Symbol: 'GOLD', Response Timeframe: 'PERIOD_M30', Bar Time: 2025.07.24 14:00, Signal Bar Time: 2025.07.24 14:00
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	🔍 Getting required threshold for signal: SELL
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   Current scenario: Trend-Following
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   Using trend_following threshold: 0.7000
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   Final required threshold: 0.7000
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	🔍 Getting required nBars_SL for signal: SELL
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   Current scenario: Trend-Following
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   Using trend_following nBars_SL: 12
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	   Final required nBars_SL: 12
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	Display: GOLD SELL 0.000
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	Initial historical data batch sent successfully.
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	OnInit: G_last_bar_time set to 2025.07.24 14:00:00 (1753365600)
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	 Order : symbol GOLD# timeframe 30 : receive GOLD PERIOD_M30 : Order total 0
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	 Order : Symbol GOLD# Buy: 0 Sell: 0 Magic: 13098
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	 Order Buy 0 Sell 0
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	🔍 Checking existing objects before EA starts...
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	🔍 Checking EA-related Objects for Symbol: GOLD# Timeframe: PERIOD_M30
2025.07.24 14:06:58.430	mt5_to_python_10_Multimodel (GOLD#,M30)	═══════════════════════════════════════════════════════════
2025.07.24 14:06:58.449	mt5_to_python_10_Multimodel (GOLD#,M30)	   (No EA-related objects found)
2025.07.24 14:06:58.449	mt5_to_python_10_Multimodel (GOLD#,M30)	═══════════════════════════════════════════════════════════
2025.07.24 14:06:58.449	mt5_to_python_10_Multimodel (GOLD#,M30)	🕒 New bar detected at: 2025.07.24 14:00
2025.07.24 14:06:58.449	mt5_to_python_10_Multimodel (GOLD#,M30)	🔄 New bar detected but no active orders - skipping order management
