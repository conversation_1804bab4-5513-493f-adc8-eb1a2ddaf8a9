

SendHttpRequest() // รับข้อมูลจาก Python Server

received_signal > signal // BUY / SELL / HOLD
received_confidence > confidence
received_bar_timestamp > bar_timestamp
received_signal_bar_timestamp > signal_bar_timestamp
received_response_symbol > symbol
received_response_timeframe_str > timeframe_str
received_sl_price > sl_price
received_tp_price > tp_price
received_class > class // STRONG_BUY/ BUY/ HOLD/ SELL/ STRONG_SELL
received_entry_price > entry_price
received_best_entry > best_entry
received_nBars_SL > nBars_SL
received_threshold > threshold
received_spread > spread
received_time_filters > time_filters
received_market_condition > market_condition
received_action_type > action_type
received_scenario_used > scenario_used // trend_following / counter_trend / none
received_trend_following_threshold > trend_following_threshold
received_trend_following_nbars > trend_following_nbars
received_counter_trend_threshold > counter_trend_threshold
received_counter_trend_nbars > counter_trend_nbars
received_trend_following_buy_confidence > trend_following_buy_confidence
received_trend_following_sell_confidence > trend_following_sell_confidence
received_counter_trend_buy_confidence > counter_trend_buy_confidence
received_counter_trend_sell_confidence > counter_trend_sell_confidence

if (received_signal == "BUY" || received_signal == "SELL")

ValidateScenarioAndThreshold(string signal, double confidence, string scenario, string market_condition)
scenario_check = ValidateScenarioAndThreshold(received_signal, received_confidence, received_scenario_used, received_market_condition);

	GetRequiredThreshold(signal)
	GetRequiredThreshold(string signal_type)
	
	required_threshold = 0.50; // default fallback
	
	if(received_scenario_used == "" || received_scenario_used == "none" || received_scenario_used == "N/A") ถ้าไม่มี scenario ที่ชัดเจน ใช้ threshold ที่ต่ำกว่าระหว่าง 2 scenario
	min_threshold = MathMin(received_trend_following_threshold, received_counter_trend_threshold);
	return min_threshold;

	if(received_scenario_used == "trend_following") required_threshold = received_trend_following_threshold;
	if(received_scenario_used == "counter_trend")   required_threshold = received_counter_trend_threshold;
	else                                                                         required_threshold = MathMax(received_trend_following_threshold, received_counter_trend_threshold);
	return required_threshold

scenario == "trend_following"  signal == "BUY"   specific_scenario_confidence = received_trend_following_buy_confidence;
scenario == "trend_following"  signal == "SELL"  specific_scenario_confidence = received_trend_following_sell_confidence;
scenario == "counter_trend"    signal == "BUY"   specific_scenario_confidence = received_counter_trend_buy_confidence;
scenario == "counter_trend"    signal == "SELL"  specific_scenario_confidence = received_counter_trend_sell_confidence;
