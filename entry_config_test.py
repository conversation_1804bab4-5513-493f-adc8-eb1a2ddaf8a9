#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Entry Conditions Comparison Test Script
สคริปต์สำหรับทดสอบและเปรียบเทียบการตั้งค่า Entry Conditions

การใช้งาน:
1. python entry_config_test.py --mode comparison    # ทดสอบเปรียบเทียบทั้งหมด
2. python entry_config_test.py --mode single --config config_1_macd_deep    # ทดสอบการตั้งค่าเดียว
3. python entry_config_test.py --mode recommendations    # แสดงคำแนะนำ
"""

import sys
import os
import argparse
import json
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import จากไฟล์หลัก
try:
    from python_LightGBM_18 import (
        ENTRY_CONFIGS,
        CURRENT_ENTRY_CONFIG,
        run_entry_config_comparison_test,
        quick_entry_config_test,
        display_entry_config_recommendations,
        create_entry_config_usage_guide,
        compare_entry_configs_for_symbol,
        generate_overall_comparison_report,
        get_recommended_entry_config
    )
    print("✅ Import ฟังก์ชันจากไฟล์หลักสำเร็จ")
except ImportError as e:
    print(f"❌ ไม่สามารถ import ฟังก์ชันจากไฟล์หลัก: {e}")
    sys.exit(1)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Entry Conditions Comparison Test')
    
    parser.add_argument('--mode', 
                       choices=['comparison', 'single', 'recommendations', 'guide'],
                       default='comparison',
                       help='โหมดการทำงาน')
    
    parser.add_argument('--config',
                       choices=list(ENTRY_CONFIGS.keys()),
                       default='config_1_macd_deep',
                       help='การตั้งค่าสำหรับโหมด single')
    
    parser.add_argument('--symbol',
                       default='GOLD',
                       help='Symbol สำหรับการทดสอบ')
    
    parser.add_argument('--timeframe',
                       type=int,
                       default=30,
                       help='Timeframe สำหรับการทดสอบ')
    
    parser.add_argument('--symbols',
                       nargs='+',
                       default=['GOLD', 'EURUSD', 'GBPUSD'],
                       help='รายการ symbols สำหรับการทดสอบ')
    
    parser.add_argument('--timeframes',
                       nargs='+',
                       type=int,
                       default=[30, 60],
                       help='รายการ timeframes สำหรับการทดสอบ')
    
    return parser.parse_args()

def run_comparison_mode(symbols, timeframes):
    """รันการทดสอบเปรียบเทียบทั้งหมด"""
    print(f"\n🚀 เริ่มการทดสอบเปรียบเทียบ Entry Conditions")
    print("=" * 80)

    print(f"🎯 การกรองข้อมูล:")
    print(f"   📈 Symbols ที่จะทดสอบ: {symbols}")
    print(f"   ⏰ Timeframes ที่จะทดสอบ: {timeframes}")
    print(f"   🔧 การตั้งค่าที่จะทดสอบ: {list(ENTRY_CONFIGS.keys())}")
    print("=" * 80)

    # รันการทดสอบ
    test_results = run_entry_config_comparison_test(symbols, timeframes)
    
    if test_results:
        successful_tests = sum(1 for r in test_results.values() if r['status'] == 'success')
        print(f"\n📊 สรุปผลการทดสอบ:")
        print(f"   ✅ สำเร็จ: {successful_tests}/{len(test_results)} การตั้งค่า")
        
        # แสดงรายละเอียดผลลัพธ์
        for config_name, result in test_results.items():
            status_icon = "✅" if result['status'] == 'success' else "❌"
            print(f"   {status_icon} {config_name}: {result['status']}")
            if result['status'] != 'success' and 'error' in result:
                print(f"      ข้อผิดพลาด: {result['error']}")
        
        # สร้างรายงานโดยรวม
        if successful_tests > 1:
            print(f"\n📋 สร้างรายงานโดยรวม...")
            try:
                overall_report = generate_overall_comparison_report()
                if overall_report:
                    print(f"✅ สร้างรายงานโดยรวมเสร็จสิ้น")
                    
                    # แสดงสรุปผลการเปรียบเทียบ
                    if 'config_performance_summary' in overall_report:
                        print(f"\n🏆 อันดับการตั้งค่า (ตามคะแนนเฉลี่ย):")
                        sorted_configs = sorted(
                            overall_report['config_performance_summary'].items(),
                            key=lambda x: x[1]['average_score'],
                            reverse=True
                        )
                        
                        for i, (config, stats) in enumerate(sorted_configs, 1):
                            print(f"   {i}. {config}: {stats['average_score']:.4f} "
                                  f"(ชนะ {stats['wins']}/{stats['total_tests']} ครั้ง)")
                
            except Exception as e:
                print(f"❌ ข้อผิดพลาดในการสร้างรายงาน: {e}")
        
        return test_results
    else:
        print(f"❌ การทดสอบไม่สำเร็จ")
        return None

def run_single_mode(config_name, symbol, timeframe):
    """รันการทดสอบการตั้งค่าเดียว"""
    print(f"\n⚡ ทดสอบด่วน: {config_name}")
    print(f"📈 Symbol: {symbol}, Timeframe: M{timeframe}")
    print("-" * 60)
    
    if config_name not in ENTRY_CONFIGS:
        print(f"❌ ไม่พบการตั้งค่า: {config_name}")
        return False
    
    print(f"🔧 การตั้งค่า: {ENTRY_CONFIGS[config_name]['name']}")
    print(f"📝 คำอธิบาย: {ENTRY_CONFIGS[config_name]['description']}")
    
    # รันการทดสอบ
    result = quick_entry_config_test(config_name, symbol, timeframe)
    
    if result:
        print(f"✅ ทดสอบเสร็จสิ้น")
        
        # ลองดูคำแนะนำสำหรับ symbol/timeframe นี้
        recommendation = get_recommended_entry_config(symbol, timeframe)
        if recommendation:
            print(f"\n💡 คำแนะนำสำหรับ {symbol} M{timeframe}:")
            print(f"   🏆 แนะนำ: {recommendation['recommended_config']}")
            print(f"   📊 คะแนน: {recommendation['score']:.4f}")
        
        return True
    else:
        print(f"❌ ทดสอบไม่สำเร็จ")
        return False

def run_recommendations_mode(symbols, timeframes):
    """แสดงคำแนะนำการตั้งค่า"""
    print(f"\n📋 แสดงคำแนะนำการตั้งค่า Entry Conditions")
    print("=" * 80)
    
    try:
        recommendations = display_entry_config_recommendations(symbols, timeframes)
        
        if recommendations:
            print(f"\n💾 บันทึกคำแนะนำเป็นไฟล์...")
            
            # บันทึกคำแนะนำเป็น JSON
            recommendations_file = f"entry_config_recommendations_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(recommendations_file, 'w', encoding='utf-8') as f:
                json.dump(recommendations, f, indent=2, ensure_ascii=False)
            
            print(f"✅ บันทึกคำแนะนำที่: {recommendations_file}")
            return recommendations
        else:
            print(f"❌ ไม่พบข้อมูลคำแนะนำ")
            return None
            
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการแสดงคำแนะนำ: {e}")
        return None

def run_guide_mode():
    """สร้างคู่มือการใช้งาน"""
    print(f"\n📖 สร้างคู่มือการใช้งาน")
    print("=" * 80)
    
    try:
        guide_file = create_entry_config_usage_guide()
        print(f"✅ สร้างคู่มือเสร็จสิ้น: {guide_file}")
        return guide_file
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการสร้างคู่มือ: {e}")
        return None

def main():
    """ฟังก์ชันหลัก"""
    print(f"🔧 Entry Conditions Comparison Test Script")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Parse arguments
    args = parse_arguments()
    
    print(f"🎯 โหมด: {args.mode}")
    print(f"📈 Symbols: {args.symbols}")
    print(f"⏰ Timeframes: {args.timeframes}")
    
    # รันตามโหมดที่เลือก
    if args.mode == 'comparison':
        result = run_comparison_mode(args.symbols, args.timeframes)
    elif args.mode == 'single':
        result = run_single_mode(args.config, args.symbol, args.timeframe)
    elif args.mode == 'recommendations':
        result = run_recommendations_mode(args.symbols, args.timeframes)
    elif args.mode == 'guide':
        result = run_guide_mode()
    else:
        print(f"❌ โหมดไม่ถูกต้อง: {args.mode}")
        result = None
    
    # สรุปผลลัพธ์
    print(f"\n" + "=" * 80)
    if result:
        print(f"✅ การทำงานเสร็จสิ้น: {args.mode}")
    else:
        print(f"❌ การทำงานไม่สำเร็จ: {args.mode}")
    
    print(f"⏰ สิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
