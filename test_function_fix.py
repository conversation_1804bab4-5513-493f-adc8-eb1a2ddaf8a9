#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไขฟังก์ชัน get_default_threshold_by_scenario และ get_default_nbars_by_scenario
"""

import sys
import os
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_default_functions():
    """ทดสอบฟังก์ชัน default"""
    print("🧪 ทดสอบฟังก์ชัน default")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import (
            get_default_threshold_by_scenario,
            get_default_nbars_by_scenario
        )
        
        # ทดสอบกับ symbols และ scenarios ต่างๆ
        test_cases = [
            ('GOLD', 30, 'trend_following'),
            ('GOLD', 30, 'counter_trend'),
            ('USDJPY', 30, 'trend_following'),
            ('USDJPY', 30, 'counter_trend'),
            ('AUDUSD', 60, 'trend_following'),
            ('AUDUSD', 60, 'counter_trend'),
        ]
        
        print("📊 ทดสอบ get_default_threshold_by_scenario:")
        for symbol, timeframe, scenario in test_cases:
            try:
                threshold = get_default_threshold_by_scenario(scenario, symbol)
                print(f"✅ {symbol} {scenario}: {threshold:.3f}")
            except Exception as e:
                print(f"❌ {symbol} {scenario}: {e}")
                return False
        
        print("\n📊 ทดสอบ get_default_nbars_by_scenario:")
        for symbol, timeframe, scenario in test_cases:
            try:
                nbars = get_default_nbars_by_scenario(scenario, symbol, timeframe)
                print(f"✅ {symbol} M{timeframe} {scenario}: {nbars}")
            except Exception as e:
                print(f"❌ {symbol} M{timeframe} {scenario}: {e}")
                return False
        
        print("\n✅ ฟังก์ชัน default ทำงานถูกต้อง")
        return True
        
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
        return False
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False

def test_optimization_function():
    """ทดสอบฟังก์ชันหาค่าที่เหมาะสม"""
    print("\n🚀 ทดสอบฟังก์ชันหาค่าที่เหมาะสม")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import run_multi_model_optimization
        
        # ทดสอบกับ GOLD M30
        symbol = "GOLD"
        timeframe = 30
        
        print(f"🔍 ทดสอบ {symbol} M{timeframe}")
        print("⚠️ การทดสอบนี้อาจใช้เวลา 1-2 นาที...")
        
        start_time = datetime.now()
        result = run_multi_model_optimization(symbol, timeframe)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        print(f"\n⏱️ เวลาที่ใช้: {duration:.1f} วินาที")
        
        if result:
            print("\n✅ การหาค่าที่เหมาะสมสำเร็จ!")
            print(f"📊 ผลลัพธ์สำหรับ {symbol} M{timeframe}:")
            
            for scenario in result['scenarios']:
                threshold = result['optimal_thresholds'].get(scenario, 'N/A')
                nbars = result['optimal_nbars'].get(scenario, 'N/A')
                print(f"   {scenario}: threshold={threshold}, nBars_SL={nbars}")
            
            return True
        else:
            print("❌ การหาค่าที่เหมาะสมไม่สำเร็จ")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_loading():
    """ทดสอบการโหลดพารามิเตอร์"""
    print("\n📁 ทดสอบการโหลดพารามิเตอร์")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import (
            load_scenario_threshold,
            load_scenario_nbars
        )
        
        # ทดสอบกับ symbols ที่เพิ่งแก้ไข
        test_cases = [
            ('GOLD', 30, 'trend_following'),
            ('GOLD', 30, 'counter_trend'),
            ('USDJPY', 30, 'trend_following'),
            ('USDJPY', 30, 'counter_trend'),
        ]
        
        success_count = 0
        
        for symbol, timeframe, scenario in test_cases:
            try:
                threshold = load_scenario_threshold(symbol, timeframe, scenario)
                nbars = load_scenario_nbars(symbol, timeframe, scenario)
                
                print(f"✅ {symbol} M{timeframe} {scenario}: T={threshold:.4f}, N={nbars}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ {symbol} M{timeframe} {scenario}: {e}")
        
        print(f"\n📊 ผลการทดสอบ: {success_count}/{len(test_cases)} สำเร็จ")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 ทดสอบการแก้ไขฟังก์ชัน")
    print("=" * 60)
    print(f"⏰ เวลาเริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ขั้นตอนการทดสอบ
    steps = [
        ("ทดสอบฟังก์ชัน default", test_default_functions),
        ("ทดสอบการโหลดพารามิเตอร์", test_parameter_loading),
        ("ทดสอบฟังก์ชันหาค่าที่เหมาะสม", test_optimization_function),
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n{'='*15} {step_name} {'='*15}")
        try:
            result = step_func()
            results.append((step_name, result))
            status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
            print(f"\n{status} {step_name}")
        except Exception as e:
            print(f"\n❌ {step_name}: เกิดข้อผิดพลาด - {e}")
            results.append((step_name, False))
    
    # สรุปผลการทดสอบ
    print(f"\n{'='*60}")
    print("📊 สรุปผลการทดสอบ:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for step_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
        print(f"   {status} {step_name}")
    
    print(f"\n🎯 ผลรวม: {passed}/{total} ขั้นตอนผ่าน")
    print(f"⏰ เวลาสิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("\n🎉 การแก้ไขสำเร็จ! ฟังก์ชันทำงานถูกต้องแล้ว")
        print("\n📋 ขั้นตอนถัดไป:")
        print("   1. รันการหาค่าที่เหมาะสมสำหรับ symbols อื่นๆ")
        print("   2. ตรวจสอบการใช้งานใน MT5 WebRequest Server")
        print("   3. ทดสอบการเทรดจริง")
    elif passed >= 2:
        print("\n✅ การแก้ไขส่วนใหญ่สำเร็จ")
        print("💡 ฟังก์ชัน default ทำงานแล้ว")
    else:
        print("\n⚠️ ยังมีปัญหาที่ต้องแก้ไข")

if __name__ == "__main__":
    main()
