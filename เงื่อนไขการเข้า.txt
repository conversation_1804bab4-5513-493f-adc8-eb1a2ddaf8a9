จากเงื่อนไขการเข้า train จาก python_LightGBM_17_Signal.py

# Entry conditions ใหม่สำหรับ 4 scenarios
entry_conditions = {
    # Scenario 1: Trend Following (เมื่อราคาอยู่เหนือ EMA200) - มีทั้ง BUY และ SELL
    "trend_following": {
        "buy": lambda prev: (
            # เงื่อนไขพื้นฐาน (ลดความเข้มงวด)
            prev['close'] > prev['open'] and
            prev['close'] > prev['ema200'] and
            # เงื่อนไขเทคนิค (ลดความเข้มงวด)
            prev['rsi14'] > input_rsi_level_in * 0.8 and  # ลดจาก 1.0 เป็น 0.8
            prev['rsi14'] < 75 and  # เพิ่มจาก 70 เป็น 75
            prev['macd_signal'] == 1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.5 and  # ลดจาก 1.0 เป็น 0.5
            # เงื่อนไขเฉพาะ trend following (ลดความเข้มงวด)
            prev['pullback_buy'] > input_pull_back * 0.5 and  # ลดจาก 0.8 เป็น 0.5
            prev['ratio_buy'] > (input_take_profit * 1.5)  # ลดจาก 2.5 เป็น 1.5
        ),
        "sell": lambda prev: (
            # SELL สวนเทรน (เมื่อราคาอยู่เหนือ EMA200 แต่ขาย) - ลดความเข้มงวด
            prev['close'] < prev['open'] and
            prev['close'] > prev['ema200'] and  # ยังอยู่เหนือ EMA200
            # เงื่อนไขเทคนิค (ลดความเข้มงวด)
            prev['rsi14'] > 65 and  # ลดจาก 70 เป็น 65
            prev['macd_signal'] == -1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and  # ลดจาก 1.2 เป็น 0.8
            # เงื่อนไขเฉพาะ counter trend (ลดความเข้มงวด)
            prev['pullback_sell'] > input_pull_back * 0.8 and  # ลดจาก 1.2 เป็น 0.8
            prev['ratio_sell'] > (input_take_profit * 1.5)  # ลดจาก 2.0 เป็น 1.5
        )
    },

    # Scenario 2: Counter Trend (เมื่อราคาอยู่ใต้ EMA200) - มีทั้ง BUY และ SELL
    "counter_trend": {
        "buy": lambda prev: (
            # BUY สวนเทรน (เมื่อราคาอยู่ใต้ EMA200 แต่ซื้อ) - ลดความเข้มงวด
            prev['close'] > prev['open'] and
            prev['close'] < prev['ema200'] and  # ยังอยู่ใต้ EMA200
            # เงื่อนไขเทคนิค (ลดความเข้มงวด)
            prev['rsi14'] < 35 and  # เพิ่มจาก 30 เป็น 35
            prev['macd_signal'] == 1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.8 and  # ลดจาก 1.2 เป็น 0.8
            # เงื่อนไขเฉพาะ counter trend (ลดความเข้มงวด)
            prev['pullback_buy'] > input_pull_back * 0.8 and  # ลดจาก 1.2 เป็น 0.8
            prev['ratio_buy'] > (input_take_profit * 1.5)  # ลดจาก 2.0 เป็น 1.5
        ),
        "sell": lambda prev: (
            # SELL ตามเทรน (เมื่อราคาอยู่ใต้ EMA200) - ลดความเข้มงวด
            prev['close'] < prev['open'] and
            prev['close'] < prev['ema200'] and
            # เงื่อนไขเทคนิค (ลดความเข้มงวด)
            prev['rsi14'] < (100 - input_rsi_level_in * 0.8) and  # ลดความเข้มงวด
            prev['rsi14'] > 25 and  # ลดจาก 30 เป็น 25
            prev['macd_signal'] == -1.0 and
            prev['volume'] > prev['volume_ma20'] * 0.5 and  # ลดจาก 1.0 เป็น 0.5
            # เงื่อนไขเฉพาะ trend following (ลดความเข้มงวด)
            prev['pullback_sell'] > input_pull_back * 0.5 and  # ลดจาก 0.8 เป็น 0.5
            prev['ratio_sell'] > (input_take_profit * 1.5)  # ลดจาก 2.5 เป็น 1.5
        )
    }
}

server จาก python_to_mt5_WebRequest_server_13_Signal.py
เงื่อขไขการเข้าไม่เหมือนกันระหว่าง train และ server เช่น
tech_signal_buy_conditions และ tech_signal_sell_conditions ใช้การเข้ากว้างๆ ไม่ได้ดูว่าเป็น trend_following หรือ counter_trend
ถ้าส่งผลต่อการวิเคราะห์สัญญาณ ต้องการให้มีความใกล้เคียงกับที่ใช้เทรน

        tech_signal_buy_conditions = {
            'Close > Open (Prev Bar)': latest_features_dict_all_i2.get('Close', -float('inf')) > latest_features_dict_all_i2.get('Open', float('inf')), << ใช้ทั้ง trend_following หรือ counter_trend เหมือนกัน
            ** เพิ่มเติมส่วนที่ไม่มี EMA200 << trend_following : close > EMA200 สำหรับ counter_trend : close < EMA200
            'RSI_signal > input_rsi_level_in * 0.8 (Prev Bar)': latest_features_dict_all_i2.get('RSI_signal', -float('inf')) > (input_rsi_level_in * 0.8),  << trend_following : rsi > 0.8*rsi_level_in และ rsi < 75 สำหรับ counter_trend : rsi < 35
            'MACD_signal == 1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == 1.0, << ใช้ทั้ง trend_following หรือ counter_trend เหมือนกัน
            'Volume > Volume_MA20 * 0.50 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.50, << trend_following : ใช้ 0.5 และ counter_trend : ใช้ 0.8
            'prev_pullback_buy > input_pull_back * 0.5': latest_features_dict_all_i2.get('PullBack_Up', 0.0) > (input_pull_back * 0.5), << trend_following : ใช้ 0.5 และ counter_trend : ใช้ 0.8
            'prev_ratio_buy > input_take_profit * 0.8': latest_features_dict_all_i2.get('Ratio_Buy', 0.0) > (input_take_profit * 0.8) << trend_following : ใช้ 1.5 และ counter_trend : ใช้ 1.5
        }

        tech_signal_sell_conditions = {
            'Close < Open (Prev Bar)': latest_features_dict_all_i2.get('Close', float('inf')) < latest_features_dict_all_i2.get('Open', -float('inf')), << ใช้ทั้ง trend_following หรือ counter_trend เหมือนกัน
            ** เพิ่มเติมส่วนที่ไม่มี EMA200 << trend_following : close < EMA200 สำหรับ counter_trend : close > EMA200
            'RSI_signal < (100-input_rsi_level_in * 0.8) (Prev Bar)': latest_features_dict_all_i2.get('RSI_signal', float('inf')) < (100 - input_rsi_level_in * 0.8),  << trend_following : rsi < 0.8*(100-rsi_level_in) และ rsi > 25 สำหรับ counter_trend : rsi > 35
            'MACD_signal == -1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == -1.0, << ใช้ทั้ง trend_following หรือ counter_trend เหมือนกัน
            'Volume > Volume_MA20 * 0.50 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.50, << trend_following : ใช้ 0.5 และ counter_trend : ใช้ 0.8
            'prev_pullback_sell > input_pull_back * 0.5': latest_features_dict_all_i2.get('PullBack_Down', 0.0) > (input_pull_back * 0.5), << trend_following : ใช้ 0.5 และ counter_trend : ใช้ 0.8
            'prev_ratio_sell > input_take_profit * 0.8': latest_features_dict_all_i2.get('Ratio_Sell', 0.0) > (input_take_profit * 0.8)  << trend_following : ใช้ 1.5 และ counter_trend : ใช้ 1.5
        }

จะเห็นว่าไม่เหมือนกันในบางกรณี
ช่วยหาแนวทางการแก้ไข ให้มีความใกล้เคียงกับ train มากที่สุด