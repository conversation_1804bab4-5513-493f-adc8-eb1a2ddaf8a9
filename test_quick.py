#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Test Script
สคริปต์ทดสอบง่ายๆ สำหรับตรวจสอบการกรองข้อมูล
"""

import sys
import os

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """ทดสอบการ import"""
    print("🔍 ทดสอบการ import")
    print("=" * 50)
    
    try:
        from python_LightGBM_18 import (
            ENTRY_CONFIGS,
            CURRENT_ENTRY_CONFIG,
            run_entry_config_comparison_test,
            run_main_analysis,
            test_groups
        )
        print("✅ Import สำเร็จ")
        print(f"   CURRENT_ENTRY_CONFIG: {CURRENT_ENTRY_CONFIG}")
        print(f"   ENTRY_CONFIGS keys: {list(ENTRY_CONFIGS.keys())}")
        print(f"   test_groups keys: {list(test_groups.keys())}")
        return True
    except ImportError as e:
        print(f"❌ Import ไม่สำเร็จ: {e}")
        return False

def test_run_main_analysis():
    """ทดสอบ run_main_analysis ด้วยพารามิเตอร์"""
    print("\n🔍 ทดสอบ run_main_analysis")
    print("=" * 50)
    
    try:
        from python_LightGBM_18 import run_main_analysis
        
        print("🧪 เรียกใช้ run_main_analysis ด้วยพารามิเตอร์:")
        print("   filter_symbols=['GOLD']")
        print("   filter_timeframes=[30]")
        
        # เรียกใช้จริง แต่จะหยุดเร็วๆ เพราะมี debug ข้อมูล
        print("\n🚀 เริ่มการทดสอบ...")
        
        # ใช้ try-except เพื่อจับ error ที่อาจเกิดขึ้น
        try:
            result = run_main_analysis(filter_symbols=['GOLD'], filter_timeframes=[30])
            print(f"✅ ทดสอบเสร็จสิ้น - ผลลัพธ์: {type(result)}")
            if result:
                print(f"   จำนวนผลลัพธ์: {len(result)}")
        except KeyboardInterrupt:
            print("\n⏹️ หยุดการทดสอบด้วย Ctrl+C")
        except Exception as e:
            print(f"❌ ข้อผิดพลาดในการทดสอบ: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")

def test_entry_config_comparison():
    """ทดสอบ run_entry_config_comparison_test"""
    print("\n🔍 ทดสอบ run_entry_config_comparison_test")
    print("=" * 50)
    
    try:
        from python_LightGBM_18 import run_entry_config_comparison_test
        
        print("🧪 เรียกใช้ run_entry_config_comparison_test:")
        print("   symbols=['GOLD']")
        print("   timeframes=[30]")
        print("   configs_to_test=['config_1_macd_deep']")
        
        print("\n🚀 เริ่มการทดสอบ...")
        
        try:
            # ทดสอบเฉพาะ 1 config เพื่อความรวดเร็ว
            result = run_entry_config_comparison_test(
                symbols=['GOLD'], 
                timeframes=[30], 
                configs_to_test=['config_1_macd_deep']
            )
            print(f"✅ ทดสอบเสร็จสิ้น - ผลลัพธ์: {type(result)}")
            if result:
                print(f"   จำนวนผลลัพธ์: {len(result)}")
                for config, status in result.items():
                    print(f"   {config}: {status.get('status', 'unknown')}")
        except KeyboardInterrupt:
            print("\n⏹️ หยุดการทดสอบด้วย Ctrl+C")
        except Exception as e:
            print(f"❌ ข้อผิดพลาดในการทดสอบ: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")

def test_filter_logic_only():
    """ทดสอบเฉพาะตรรกะการกรอง"""
    print("\n🔍 ทดสอบเฉพาะตรรกะการกรอง")
    print("=" * 50)
    
    try:
        from python_LightGBM_18 import test_groups
        
        filter_symbols = ['GOLD']
        filter_timeframes = [30]
        
        print(f"Input:")
        print(f"   filter_symbols: {filter_symbols}")
        print(f"   filter_timeframes: {filter_timeframes}")
        print(f"   test_groups keys: {list(test_groups.keys())}")
        
        filtered_test_groups = {}
        
        for group_name, group_files in test_groups.items():
            print(f"\n🔍 ตรวจสอบ {group_name}:")
            print(f"   จำนวนไฟล์: {len(group_files)}")
            
            # ตรวจสอบ timeframe
            if filter_timeframes:
                timeframe_num = int(group_name[1:])  # M30 -> 30, M60 -> 60
                print(f"   timeframe_num: {timeframe_num}")
                if timeframe_num not in filter_timeframes:
                    print(f"   ⏭️ ข้าม {group_name} (ไม่อยู่ใน timeframes ที่ระบุ)")
                    continue
            
            # กรองไฟล์ตาม symbols
            filtered_files = []
            for file_path in group_files:
                file_name = os.path.basename(file_path)
                symbol = file_name.split('_')[0]
                
                if filter_symbols and symbol not in filter_symbols:
                    continue
                
                filtered_files.append(file_path)
                print(f"   ✅ รวม {file_name}")
            
            if filtered_files:
                filtered_test_groups[group_name] = filtered_files
                print(f"   📊 ผลลัพธ์: {len(filtered_files)} ไฟล์")
        
        print(f"\n📊 สรุปผลการกรอง:")
        print(f"   กลุ่มที่ผ่านการกรอง: {len(filtered_test_groups)}")
        total_files = sum(len(files) for files in filtered_test_groups.values())
        print(f"   ไฟล์ทั้งหมด: {total_files}")
        
        for group_name, files in filtered_test_groups.items():
            print(f"   {group_name}: {len(files)} ไฟล์")
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("⚡ Quick Test Script")
    print("=" * 50)
    
    # ทดสอบการ import ก่อน
    if not test_import():
        return
    
    print("\n💡 เลือกการทดสอบ:")
    print("1. ทดสอบเฉพาะตรรกะการกรอง (เร็ว)")
    print("2. ทดสอบ run_main_analysis (ช้า)")
    print("3. ทดสอบ run_entry_config_comparison_test (ช้ามาก)")
    
    # สำหรับการทดสอบอัตโนมัติ ให้รันเฉพาะตรรกะการกรอง
    print("\n🚀 รันการทดสอบเฉพาะตรรกะการกรอง...")
    test_filter_logic_only()
    
    print("\n💡 หากต้องการทดสอบเพิ่มเติม:")
    print("   - แก้ไขไฟล์นี้แล้วเรียกใช้ฟังก์ชันอื่น")
    print("   - หรือรันคำสั่ง: python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30")

if __name__ == "__main__":
    main()
