
[2025-07-15 20:44:12.806289] ERROR in GOLD M30:
Exception: Cannot set a DataFrame with multiple columns to the single column Volume_MA20
Traceback:
Traceback (most recent call last):
  File "D:\test_gold\python_to_mt5_WebRequest_server_13_Signal.py", line 634, in process_data_and_trade
    df_ft['Volume_MA20'] = df_ft['Volume'].rolling(20, min_periods=1).mean()
    ~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4301, in __setitem__
    self._set_item_frame_value(key, value)
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4459, in _set_item_frame_value
    raise ValueError(
ValueError: Cannot set a DataFrame with multiple columns to the single column Volume_MA20

==================================================

[2025-07-15 20:47:18.573826] ERROR in GOLD M30:
Exception: Cannot set a DataFrame with multiple columns to the single column Volume_Lag_1
Traceback:
Traceback (most recent call last):
  File "D:\test_gold\python_to_mt5_WebRequest_server_13_Signal.py", line 966, in process_data_and_trade
    lag_features[f'{col}_Lag_{lag}'] = df_ft[col].shift(lag)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4301, in __setitem__
    self._set_item_frame_value(key, value)
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4459, in _set_item_frame_value
    raise ValueError(
ValueError: Cannot set a DataFrame with multiple columns to the single column Volume_Lag_1

==================================================

[2025-07-22 11:14:22.880325] ERROR in GBPUSD M30:
Exception: 'High_Prev_Max'
Traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'High_Prev_Max'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "d:\test_gold\python_to_mt5_WebRequest_server_13_Signal.py", line 1872, in process_data_and_trade
    sl_prev_bars = max(df_ft['High_Prev_Max'].iloc[-1], entry_price + 2*symbol_points)
                       ~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'High_Prev_Max'

==================================================
