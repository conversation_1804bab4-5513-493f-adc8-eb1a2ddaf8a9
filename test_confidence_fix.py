#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify confidence fix and MT5 integration
"""

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_test_data():
    """สร้างข้อมูลทดสอบที่มี signal แข็งแกร่ง"""
    print("🔍 Creating test data...")
    
    # สร้างข้อมูล 210 bars
    dates = pd.date_range(start='2025-07-24 10:00:00', periods=210, freq='30min')
    
    # สร้าง downtrend data สำหรับ SELL signal
    base_price = 3370.0
    prices = []
    
    for i in range(210):
        # สร้าง downtrend
        trend_price = base_price - (i * 0.5)  # ลดลงเรื่อยๆ
        noise = np.random.normal(0, 2.0)  # เพิ่ม noise เล็กน้อย
        price = trend_price + noise
        prices.append(price)
    
    # สร้าง OHLC data
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        high = price + np.random.uniform(1, 3)
        low = price - np.random.uniform(1, 3)
        open_price = price + np.random.uniform(-1, 1)
        close = price
        
        data.append({
            'Date': date.strftime('%Y.%m.%d'),
            'Time': date.strftime('%H:%M'),
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close, 2),
            'Volume': np.random.randint(100, 1000)
        })
    
    print(f"✅ Created {len(data)} bars with downtrend (for SELL signal)")
    print(f"   Price range: {min(prices):.2f} - {max(prices):.2f}")
    
    return data

def test_server_response():
    """ทดสอบการตอบสนองของเซิร์ฟเวอร์"""
    print("\n🚀 Testing server response...")
    
    # สร้างข้อมูลทดสอบ
    test_data = create_test_data()
    
    # ส่งข้อมูลไปยังเซิร์ฟเวอร์
    url = "http://localhost:54321/data"
    
    payload = {
        "symbol": "GOLD#",
        "timeframe_str": "PERIOD_M30",
        "data": test_data
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Server response: HTTP 200")
            print("\n📊 Response Analysis:")
            print("=" * 50)
            
            # แสดงข้อมูลสำคัญ
            signal = result.get('signal', 'N/A')
            confidence = result.get('confidence', 0.0)
            threshold = result.get('threshold', 0.0)
            scenario_used = result.get('scenario_used', 'N/A')
            market_condition = result.get('market_condition', 'N/A')
            
            print(f"Signal: {signal}")
            print(f"Confidence: {confidence}")
            print(f"Threshold: {threshold}")
            print(f"Scenario: {scenario_used}")
            print(f"Market: {market_condition}")
            print(f"Entry Price: {result.get('entry_price', 0.0)}")
            print(f"SL Price: {result.get('sl_price', 0.0)}")
            print(f"TP Price: {result.get('tp_price', 0.0)}")
            
            # ตรวจสอบเงื่อนไขการเทรด
            print(f"\n🎯 Trading Analysis:")
            print("=" * 50)
            
            will_trade = (
                signal in ['BUY', 'SELL'] and 
                confidence > 0.0 and 
                confidence > threshold and
                result.get('entry_price', 0.0) > 0 and
                result.get('sl_price', 0.0) > 0 and
                result.get('tp_price', 0.0) > 0
            )
            
            if will_trade:
                print("🎉 SUCCESS: MT5 should execute trade!")
                print(f"   ✅ Signal: {signal} (valid)")
                print(f"   ✅ Confidence: {confidence:.4f} > {threshold:.4f}")
                print(f"   ✅ Price data: Complete")
                
                # ตรวจสอบ scenario matching
                if market_condition == "downtrend" and signal == "SELL":
                    if scenario_used in ["trend_following", "Trend-Following"]:
                        print(f"   ✅ Scenario: {scenario_used} matches downtrend SELL")
                    else:
                        print(f"   ⚠️ Scenario: {scenario_used} (unexpected for downtrend SELL)")
                
            else:
                print("❌ FAIL: MT5 will NOT execute trade")
                reasons = []
                if signal not in ['BUY', 'SELL']:
                    reasons.append(f"Signal is {signal} (not BUY/SELL)")
                if confidence <= 0.0:
                    reasons.append(f"Confidence is {confidence:.4f} (zero or negative)")
                if confidence <= threshold:
                    reasons.append(f"Confidence {confidence:.4f} <= threshold {threshold:.4f}")
                if result.get('entry_price', 0.0) <= 0:
                    reasons.append("No entry price")
                if result.get('sl_price', 0.0) <= 0:
                    reasons.append("No SL price")
                if result.get('tp_price', 0.0) <= 0:
                    reasons.append("No TP price")
                
                print("   Reasons:")
                for reason in reasons:
                    print(f"   - {reason}")
            
            # แสดงข้อมูล Multi-Model Analysis
            analysis_summary = result.get('analysis_summary', {})
            if analysis_summary:
                print(f"\n📈 Multi-Model Analysis:")
                print("=" * 50)
                
                for scenario in ['trend_following', 'counter_trend']:
                    if scenario in analysis_summary:
                        scenario_data = analysis_summary[scenario]
                        print(f"\n{scenario.upper()}:")
                        
                        for action in ['buy', 'sell']:
                            if action in scenario_data:
                                action_data = scenario_data[action]
                                should_trade = action_data.get('should_trade', False)
                                conf = action_data.get('confidence', 0.0)
                                status = "✅" if should_trade else "❌"
                                print(f"  {status} {action.upper()}: {conf:.4f} (trade: {should_trade})")
            
            return will_trade
            
        else:
            print(f"❌ Server error: HTTP {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing Confidence Fix and MT5 Integration")
    print("=" * 60)
    
    success = test_server_response()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Test PASSED - System ready for trading")
    else:
        print("❌ Test FAILED - Issues need to be resolved")
