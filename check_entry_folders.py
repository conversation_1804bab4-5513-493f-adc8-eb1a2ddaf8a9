#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Check Entry Folders Script
สคริปต์ตรวจสอบโฟลเดอร์และไฟล์ที่เกี่ยวข้องกับ Entry Config Comparison
"""

import os
import json
from datetime import datetime

def check_entry_folders():
    """ตรวจสอบโฟลเดอร์ Entry Config"""
    print("🔍 ตรวจสอบโฟลเดอร์ Entry Config")
    print("=" * 50)
    
    configs = [
        "config_1_macd_deep",
        "config_2_macd_signal", 
        "config_3_enhanced_deep",
        "config_4_enhanced_signal"
    ]
    
    for config in configs:
        folder_name = f"LightGBM_Entry_{config}"
        print(f"\n📁 ตรวจสอบ {folder_name}:")
        
        if os.path.exists(folder_name):
            print(f"   ✅ โฟลเดอร์มีอยู่")
            
            # ตรวจสอบโครงสร้างโฟลเดอร์
            results_folder = os.path.join(folder_name, "results")
            if os.path.exists(results_folder):
                print(f"   ✅ โฟลเดอร์ results มีอยู่")
                
                # ตรวจสอบไฟล์ GOLD M30
                gold_folder = os.path.join(results_folder, "030_GOLD")
                if os.path.exists(gold_folder):
                    print(f"   ✅ โฟลเดอร์ 030_GOLD มีอยู่")
                    
                    performance_file = os.path.join(gold_folder, "performance_summary.json")
                    if os.path.exists(performance_file):
                        print(f"   ✅ ไฟล์ performance_summary.json มีอยู่")
                        
                        # อ่านและแสดงข้อมูล
                        try:
                            with open(performance_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            
                            print(f"   📊 ข้อมูลในไฟล์:")
                            print(f"      Win Rate: {data.get('win_rate', 0):.2%}")
                            print(f"      Expectancy: {data.get('expectancy', 0):.4f}")
                            print(f"      Trades: {data.get('num_trades', 0)}")
                            print(f"      Training Type: {data.get('training_type', 'unknown')}")
                            
                        except Exception as e:
                            print(f"   ❌ ข้อผิดพลาดในการอ่านไฟล์: {e}")
                    else:
                        print(f"   ❌ ไม่พบไฟล์ performance_summary.json")
                else:
                    print(f"   ❌ ไม่พบโฟลเดอร์ 030_GOLD")
            else:
                print(f"   ❌ ไม่พบโฟลเดอร์ results")
        else:
            print(f"   ❌ ไม่พบโฟลเดอร์ {folder_name}")

def check_lightgbm_multi():
    """ตรวจสอบโฟลเดอร์ LightGBM_Multi"""
    print("\n🔍 ตรวจสอบโฟลเดอร์ LightGBM_Multi")
    print("=" * 50)
    
    folder_name = "LightGBM_Multi"
    if os.path.exists(folder_name):
        print(f"✅ โฟลเดอร์ {folder_name} มีอยู่")
        
        # ตรวจสอบไฟล์ที่เกี่ยวข้องกับ GOLD M30
        gold_files = []
        for root, dirs, files in os.walk(folder_name):
            for file in files:
                if "GOLD" in file and ("030" in file or "M30" in file):
                    gold_files.append(os.path.join(root, file))
        
        print(f"📁 ไฟล์ที่เกี่ยวข้องกับ GOLD M30: {len(gold_files)} ไฟล์")
        for file_path in gold_files[:10]:  # แสดงเฉพาะ 10 ไฟล์แรก
            print(f"   - {file_path}")
        
        if len(gold_files) > 10:
            print(f"   ... และอีก {len(gold_files) - 10} ไฟล์")
    else:
        print(f"❌ ไม่พบโฟลเดอร์ {folder_name}")

def check_comparison_results():
    """ตรวจสอบโฟลเดอร์ Entry_Comparison_Results"""
    print("\n🔍 ตรวจสอบโฟลเดอร์ Entry_Comparison_Results")
    print("=" * 50)
    
    folder_name = "Entry_Comparison_Results"
    if os.path.exists(folder_name):
        print(f"✅ โฟลเดอร์ {folder_name} มีอยู่")
        
        files = os.listdir(folder_name)
        print(f"📁 ไฟล์ในโฟลเดอร์: {len(files)} ไฟล์")
        for file in files:
            print(f"   - {file}")
    else:
        print(f"❌ ไม่พบโฟลเดอร์ {folder_name}")

def create_test_performance_file():
    """สร้างไฟล์ทดสอบ performance_summary.json"""
    print("\n🔧 สร้างไฟล์ทดสอบ performance_summary.json")
    print("=" * 50)
    
    configs = [
        "config_1_macd_deep",
        "config_2_macd_signal", 
        "config_3_enhanced_deep",
        "config_4_enhanced_signal"
    ]
    
    for i, config in enumerate(configs):
        folder_name = f"LightGBM_Entry_{config}"
        results_folder = os.path.join(folder_name, "results", "030_GOLD")
        
        # สร้างโฟลเดอร์
        os.makedirs(results_folder, exist_ok=True)
        
        # สร้างข้อมูลทดสอบ
        test_data = {
            'symbol': 'GOLD',
            'timeframe': 30,
            'entry_config': config,
            'entry_config_name': f"Test Config {i+1}",
            'entry_config_description': f"Test description for {config}",
            'win_rate': 0.45 + (i * 0.05),  # 45%, 50%, 55%, 60%
            'expectancy': 0.3 + (i * 0.1),   # 0.3, 0.4, 0.5, 0.6
            'profit_factor': 1.2 + (i * 0.2), # 1.2, 1.4, 1.6, 1.8
            'num_trades': 30 + (i * 10),      # 30, 40, 50, 60
            'max_drawdown': 15 - (i * 2),     # 15%, 13%, 11%, 9%
            'model_accuracy': 0.65 + (i * 0.02),
            'model_auc': 0.70 + (i * 0.02),
            'model_f1': 0.55 + (i * 0.02),
            'training_date': datetime.now().isoformat(),
            'training_type': 'multi_model',
            'num_features': 150 + (i * 5),
            'test_file': True  # ระบุว่าเป็นไฟล์ทดสอบ
        }
        
        # บันทึกไฟล์
        performance_file = os.path.join(results_folder, "performance_summary.json")
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ สร้างไฟล์ทดสอบ: {performance_file}")

def test_comparison_system():
    """ทดสอบระบบเปรียบเทียบ"""
    print("\n🧪 ทดสอบระบบเปรียบเทียบ")
    print("=" * 50)
    
    try:
        import sys
        sys.path.append('.')
        from python_LightGBM_18 import compare_entry_configs_for_symbol
        
        print("🚀 รันการเปรียบเทียบ GOLD M30...")
        result = compare_entry_configs_for_symbol('GOLD', 30)
        
        if result:
            print("✅ การเปรียบเทียบสำเร็จ")
            print(f"🏆 การตั้งค่าที่ดีที่สุด: {result.get('best_config', 'ไม่ระบุ')}")
            print(f"📊 คะแนน: {result.get('best_score', 0):.4f}")
        else:
            print("❌ การเปรียบเทียบไม่สำเร็จ")
            
    except Exception as e:
        print(f"❌ ข้อผิดพลาดในการทดสอบ: {e}")

def main():
    print("🔍 Check Entry Folders Script")
    print(f"⏰ เริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # ตรวจสอบโฟลเดอร์ต่างๆ
    check_entry_folders()
    check_lightgbm_multi()
    check_comparison_results()
    
    # สร้างไฟล์ทดสอบ
    print("\n" + "=" * 50)
    create_test_performance_file()
    
    # ทดสอบระบบเปรียบเทียบ
    print("\n" + "=" * 50)
    test_comparison_system()
    
    print(f"\n⏰ สิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n💡 ขั้นตอนต่อไป:")
    print("1. รันคำสั่ง: python simple_entry_test.py check_results")
    print("2. รันคำสั่ง: python entry_config_test.py --mode recommendations")
    print("3. ตรวจสอบว่าระบบเปรียบเทียบทำงานถูกต้อง")

if __name__ == "__main__":
    main()
