#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไขปัญหาไฟล์พารามิเตอร์ที่หายไป
"""

import sys
import os
import glob
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def check_missing_parameters():
    """ตรวจสอบไฟล์พารามิเตอร์ที่หายไป"""
    print("🔍 ตรวจสอบไฟล์พารามิเตอร์ที่หายไป")
    print("=" * 50)
    
    symbols = ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
    timeframes = [30, 60]
    scenarios = ['trend_following', 'counter_trend']
    
    missing_files = []
    
    for symbol in symbols:
        for timeframe in timeframes:
            for scenario in scenarios:
                # ตรวจสอบไฟล์ threshold
                threshold_file = f"LightGBM_Multi/thresholds/{timeframe:03d}_{symbol}_{scenario}_optimal_threshold.pkl"
                if not os.path.exists(threshold_file):
                    missing_files.append((symbol, timeframe, scenario, 'threshold'))
                
                # ตรวจสอบไฟล์ nBars_SL
                nbars_file = f"LightGBM_Multi/thresholds/{timeframe:03d}_{symbol}_{scenario}_optimal_nBars_SL.pkl"
                if not os.path.exists(nbars_file):
                    missing_files.append((symbol, timeframe, scenario, 'nBars_SL'))
    
    if missing_files:
        print(f"❌ พบไฟล์ที่หายไป: {len(missing_files)} ไฟล์")
        
        # จัดกลุ่มตาม symbol และ timeframe
        missing_by_symbol = {}
        for symbol, timeframe, scenario, param_type in missing_files:
            key = f"{symbol}_M{timeframe}"
            if key not in missing_by_symbol:
                missing_by_symbol[key] = []
            missing_by_symbol[key].append(f"{scenario}_{param_type}")
        
        for symbol_tf, missing_params in missing_by_symbol.items():
            print(f"   💰 {symbol_tf}: {', '.join(missing_params)}")
        
        return missing_by_symbol
    else:
        print("✅ ไม่พบไฟล์ที่หายไป")
        return {}

def fix_missing_parameters():
    """แก้ไขไฟล์พารามิเตอร์ที่หายไป"""
    print("\n🛠️ แก้ไขไฟล์พารามิเตอร์ที่หายไป")
    print("=" * 50)
    
    missing_symbols = check_missing_parameters()
    
    if not missing_symbols:
        print("✅ ไม่มีไฟล์ที่ต้องแก้ไข")
        return True
    
    try:
        from python_LightGBM_17_Signal import run_multi_model_optimization
        
        # แยก symbols และ timeframes ที่ต้องรัน
        symbols_to_run = set()
        timeframes_to_run = set()
        
        for symbol_tf in missing_symbols.keys():
            parts = symbol_tf.split('_M')
            symbol = parts[0]
            timeframe = int(parts[1])
            symbols_to_run.add(symbol)
            timeframes_to_run.add(timeframe)
        
        print(f"📊 จะรันการหาค่าที่เหมาะสมสำหรับ:")
        for symbol_tf in missing_symbols.keys():
            print(f"   💰 {symbol_tf}")
        
        success_count = 0
        total_count = len(missing_symbols)
        
        for symbol_tf in missing_symbols.keys():
            parts = symbol_tf.split('_M')
            symbol = parts[0]
            timeframe = int(parts[1])
            
            print(f"\n🚀 กำลังรัน {symbol} M{timeframe}")
            print(f"⏰ เวลา: {datetime.now().strftime('%H:%M:%S')}")
            
            try:
                start_time = datetime.now()
                result = run_multi_model_optimization(symbol, timeframe)
                end_time = datetime.now()
                
                duration = (end_time - start_time).total_seconds()
                
                if result:
                    print(f"✅ สำเร็จ ({duration:.1f}s)")
                    
                    # แสดงผลลัพธ์
                    for scenario in result['scenarios']:
                        threshold = result['optimal_thresholds'].get(scenario, 'N/A')
                        nbars = result['optimal_nbars'].get(scenario, 'N/A')
                        print(f"   {scenario}: threshold={threshold}, nBars_SL={nbars}")
                    
                    success_count += 1
                else:
                    print(f"❌ ไม่สำเร็จ ({duration:.1f}s)")
                    
            except Exception as e:
                print(f"❌ เกิดข้อผิดพลาด: {e}")
        
        print(f"\n📊 สรุปผลลัพธ์:")
        print(f"   สำเร็จ: {success_count}/{total_count}")
        print(f"   อัตราความสำเร็จ: {success_count/total_count*100:.1f}%")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการแก้ไข: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_importance_fix():
    """ทดสอบการแก้ไข Feature Importance"""
    print("\n🧪 ทดสอบการแก้ไข Feature Importance")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import analyze_cross_asset_feature_importance
        
        # ทดสอบกับไฟล์ตัวอย่าง
        test_files = ['GOLD#_M30_250711.csv']
        importance_dir = 'LightGBM_Multi/results/M30'
        output_path = 'test_features.pkl'
        
        print("🔍 ทดสอบการวิเคราะห์ Feature Importance")
        
        features = analyze_cross_asset_feature_importance(
            input_files=test_files,
            importance_files_dir=importance_dir,
            pickle_output_path=output_path,
            num_top_features_per_asset=15,
            min_assets_threshold=1,
            overall_top_n=8
        )
        
        print(f"\n📊 Features ที่ได้:")
        for i, feature in enumerate(features, 1):
            print(f"   {i}. {feature}")
        
        # ตรวจสอบว่ามี Target หรือไม่
        target_found = any('target' in feature.lower() for feature in features)
        
        if target_found:
            print("\n❌ ยังพบ Target ใน features")
            return False
        else:
            print("\n✅ ไม่พบ Target ใน features แล้ว")
            
            # ลบไฟล์ทดสอบ
            if os.path.exists(output_path):
                os.remove(output_path)
            
            return True
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 แก้ไขปัญหาไฟล์พารามิเตอร์และ Feature Importance")
    print("=" * 70)
    print(f"⏰ เวลาเริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ทดสอบการแก้ไข Feature Importance ก่อน
    print("\n1️⃣ ทดสอบการแก้ไข Feature Importance")
    feature_fix_success = test_feature_importance_fix()
    
    # แก้ไขไฟล์พารามิเตอร์ที่หายไป
    print("\n2️⃣ แก้ไขไฟล์พารามิเตอร์ที่หายไป")
    param_fix_success = fix_missing_parameters()
    
    # สรุปผลลัพธ์
    print(f"\n{'='*70}")
    print("📊 สรุปผลการแก้ไข:")
    print(f"   ✅ Feature Importance: {'สำเร็จ' if feature_fix_success else 'ไม่สำเร็จ'}")
    print(f"   ✅ Missing Parameters: {'สำเร็จ' if param_fix_success else 'ไม่สำเร็จ'}")
    
    if feature_fix_success and param_fix_success:
        print("\n🎉 การแก้ไขทั้งหมดสำเร็จ!")
        print("\n📋 ขั้นตอนถัดไป:")
        print("   1. รันการสร้างรายงานสรุปใหม่")
        print("   2. ตรวจสอบการใช้งานใน MT5 WebRequest Server")
        print("   3. ทดสอบการเทรดจริง")
    else:
        print("\n⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        
        if not feature_fix_success:
            print("   - Feature Importance ยังมี Target ใน features")
        if not param_fix_success:
            print("   - ไฟล์พารามิเตอร์บางไฟล์ยังหายไป")
    
    print(f"\n⏰ เวลาสิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
