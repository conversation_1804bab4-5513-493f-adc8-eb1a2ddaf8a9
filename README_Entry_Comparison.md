# 🔧 Entry Conditions Comparison System

ระบบเปรียบเทียบการตั้งค่า Entry Conditions สำหรับ Trading Model ที่ช่วยหาการตั้งค่าที่ให้ผลลัพธ์ดีที่สุดสำหรับแต่ละ symbol และ timeframe

## 📋 ภาพรวม

ระบบนี้ช่วยเปรียบเทียบการตั้งค่า Entry Conditions ทั้ง 4 แบบ:

1. **config_1_macd_deep**: ใช้ MACD Deep Signal
2. **config_2_macd_signal**: ใช้ MACD Signal  
3. **config_3_enhanced_deep**: ใช้ MACD Deep + เงื่อนไขเพิ่มเติม
4. **config_4_enhanced_signal**: ใช้ MACD Signal + เงื่อนไขเพิ่มเติม

## 🚀 การใช้งาน

### 1. การทดสอบเปรียบเทียบทั้งหมด

```bash
# ทดสอบทั้ง 4 การตั้งค่าสำหรับ symbols และ timeframes ที่กำหนด
python entry_config_test.py --mode comparison --symbols GOLD EURUSD --timeframes 30 60
```

### 2. การทดสอบการตั้งค่าเดียว

```bash
# ทดสอบการตั้งค่าเฉพาะ
python entry_config_test.py --mode single --config config_1_macd_deep --symbol GOLD --timeframe 30
```

### 3. การดูคำแนะนำ

```bash
# แสดงคำแนะนำการตั้งค่าที่ดีที่สุด
python entry_config_test.py --mode recommendations --symbols GOLD EURUSD --timeframes 30 60
```

### 4. การสร้างคู่มือ

```bash
# สร้างคู่มือการใช้งาน
python entry_config_test.py --mode guide
```

## 📊 การตีความผลลัพธ์

### คะแนนการประเมิน (Score)
- **0.8-1.0**: ดีเยี่ยม ⭐⭐⭐⭐⭐
- **0.6-0.8**: ดี ⭐⭐⭐⭐
- **0.4-0.6**: ปานกลาง ⭐⭐⭐
- **0.0-0.4**: ต่ำ ⭐⭐

### เมตริกสำคัญ
- **Win Rate**: อัตราการชนะ (เป้าหมาย 45-55%)
- **Expectancy**: ผลกำไรเฉลี่ยต่อการเทรด (เป้าหมาย > 0.5)
- **Profit Factor**: อัตราส่วนกำไรต่อขาดทุน (เป้าหมาย > 1.5)
- **จำนวนเทรด**: ควรมีอย่างน้อย 30 เทรดเพื่อความน่าเชื่อถือ

## 📁 โครงสร้างไฟล์ผลลัพธ์

```
LightGBM_Entry_config_1_macd_deep/
├── models/                     # โมเดลที่เทรน
├── results/                    # ผลลัพธ์การเทรน
└── performance_summary.json    # สรุปผลการประเมิน

LightGBM_Entry_config_2_macd_signal/
├── models/
├── results/
└── performance_summary.json

LightGBM_Entry_config_3_enhanced_deep/
├── models/
├── results/
└── performance_summary.json

LightGBM_Entry_config_4_enhanced_signal/
├── models/
├── results/
└── performance_summary.json

Entry_Comparison_Results/
├── 030_GOLD_comparison.json         # ผลการเปรียบเทียบแต่ละ symbol/timeframe
├── 030_EURUSD_comparison.json
├── 060_GOLD_comparison.json
├── overall_comparison_report.json   # รายงานโดยรวม
└── best_configs_summary.csv         # สรุปการตั้งค่าที่ดีที่สุด
```

## 🔧 การปรับแต่งการตั้งค่า

### การเปลี่ยนการตั้งค่าปัจจุบัน

```python
# ในไฟล์ python_LightGBM_18.py
CURRENT_ENTRY_CONFIG = "config_2_macd_signal"  # เปลี่ยนเป็นการตั้งค่าที่ต้องการ
entry_conditions = ENTRY_CONFIGS[CURRENT_ENTRY_CONFIG]["conditions"]
```

### การเพิ่มการตั้งค่าใหม่

```python
# เพิ่มในส่วน ENTRY_CONFIGS
ENTRY_CONFIGS["config_5_custom"] = {
    "name": "Custom Configuration",
    "description": "การตั้งค่าที่กำหนดเอง",
    "conditions": {
        "trend_following": {
            "buy": lambda prev: (
                # เงื่อนไขการซื้อแบบ trend following
            ),
            "sell": lambda prev: (
                # เงื่อนไขการขายแบบ trend following
            )
        },
        "counter_trend": {
            "buy": lambda prev: (
                # เงื่อนไขการซื้อแบบ counter trend
            ),
            "sell": lambda prev: (
                # เงื่อนไขการขายแบบ counter trend
            )
        }
    }
}
```

## 📈 ตัวอย่างผลลัพธ์

### รายงานการเปรียบเทียบ
```
🏆 การตั้งค่าที่ดีที่สุดสำหรับ GOLD M30: config_3_enhanced_deep (Score: 0.7845)
📊 ผลลัพธ์:
   - Win Rate: 52.3%
   - Expectancy: 0.68
   - Profit Factor: 1.85
   - จำนวนเทรด: 45
```

### สรุปการแนะนำ
```
🔧 config_3_enhanced_deep: 6/8 (75.0%)
   📝 Enhanced MACD Deep
🔧 config_1_macd_deep: 2/8 (25.0%)
   📝 MACD Deep Signal
```

## ⚠️ ข้อควรระวัง

1. **ข้อมูลเพียงพอ**: ควรมีข้อมูลอย่างน้อย 1000 แถวสำหรับการเทรน
2. **จำนวนเทรด**: ผลลัพธ์ที่มีเทรดน้อยกว่า 20 เทรดอาจไม่น่าเชื่อถือ
3. **Market Conditions**: การตั้งค่าที่ดีในช่วงหนึ่งอาจไม่เหมาะกับช่วงอื่น
4. **Overfitting**: ควรทดสอบกับข้อมูล out-of-sample เสมอ

## 🔄 การปรับปรุงต่อเนื่อง

1. **ทดสอบเป็นระยะ**: ทดสอบการตั้งค่าใหม่ทุกเดือน
2. **ติดตามผลจริง**: เปรียบเทียบผลการเทรดจริงกับการทดสอบ
3. **ปรับปรุงเกณฑ์**: ปรับเกณฑ์การให้คะแนนตามประสบการณ์
4. **เพิ่มการตั้งค่า**: เพิ่มการตั้งค่าใหม่ตามความต้องการ

## 🛠️ การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **Import Error**: ตรวจสอบว่าไฟล์ python_LightGBM_18.py อยู่ในโฟลเดอร์เดียวกัน
2. **ไม่มีข้อมูล**: ตรวจสอบว่ามีไฟล์ CSV ในโฟลเดอร์ CSV_Files_Fixed
3. **Memory Error**: ลดจำนวน symbols หรือ timeframes ที่ทดสอบ
4. **ผลลัพธ์ไม่สมเหตุสมผล**: ตรวจสอบการตั้งค่าพารามิเตอร์

### การ Debug

```python
# เปิดใช้งาน debug mode
Steps_to_do = True  # ในไฟล์ python_LightGBM_18.py

# ตรวจสอบการตั้งค่าปัจจุบัน
print(f"Current config: {CURRENT_ENTRY_CONFIG}")
print(f"Available configs: {list(ENTRY_CONFIGS.keys())}")
```

## 📞 การติดต่อ

หากมีปัญหาหรือข้อเสนอแนะ กรุณาติดต่อผู้พัฒนาระบบ

---
📅 อัปเดตล่าสุด: 2025-01-25
🔧 เวอร์ชัน: 1.0.0
