#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
แก้ไขปัญหาไฟล์พารามิเตอร์ที่หายไปและสร้างรายงานใหม่
"""

import sys
import os
import glob
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def check_missing_files():
    """ตรวจสอบไฟล์ที่หายไป"""
    print("🔍 ตรวจสอบไฟล์พารามิเตอร์ที่หายไป")
    print("=" * 50)
    
    symbols = ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
    timeframes = [30, 60]
    scenarios = ['trend_following', 'counter_trend']
    
    missing_files = []
    
    for symbol in symbols:
        for timeframe in timeframes:
            for scenario in scenarios:
                # ตรวจสอบไฟล์ threshold
                threshold_file = f"LightGBM_Multi/thresholds/{timeframe:03d}_{symbol}_{scenario}_optimal_threshold.pkl"
                if not os.path.exists(threshold_file):
                    missing_files.append((symbol, timeframe, scenario, 'threshold'))
                
                # ตรวจสอบไฟล์ nBars_SL
                nbars_file = f"LightGBM_Multi/thresholds/{timeframe:03d}_{symbol}_{scenario}_optimal_nBars_SL.pkl"
                if not os.path.exists(nbars_file):
                    missing_files.append((symbol, timeframe, scenario, 'nBars_SL'))
    
    if missing_files:
        print(f"❌ พบไฟล์ที่หายไป: {len(missing_files)} ไฟล์")
        
        # จัดกลุ่มตาม symbol และ timeframe
        missing_by_symbol = {}
        for symbol, timeframe, scenario, param_type in missing_files:
            key = f"{symbol}_M{timeframe}"
            if key not in missing_by_symbol:
                missing_by_symbol[key] = []
            missing_by_symbol[key].append(f"{scenario}_{param_type}")
        
        for symbol_tf, missing_params in missing_by_symbol.items():
            print(f"   💰 {symbol_tf}: {', '.join(missing_params)}")
        
        return missing_files
    else:
        print("✅ ไม่พบไฟล์ที่หายไป")
        return []

def create_missing_files():
    """สร้างไฟล์ที่หายไปด้วยค่า default"""
    print("\n🛠️ สร้างไฟล์พารามิเตอร์ที่หายไป")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import create_missing_parameter_files
        
        created_count = create_missing_parameter_files()
        
        if created_count > 0:
            print(f"✅ สร้างไฟล์สำเร็จ: {created_count} ไฟล์")
            return True
        else:
            print("ℹ️ ไม่มีไฟล์ที่ต้องสร้าง")
            return True
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้างไฟล์: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_all_files():
    """ตรวจสอบว่าไฟล์ครบถ้วนแล้ว"""
    print("\n🔍 ตรวจสอบไฟล์หลังการแก้ไข")
    print("=" * 50)
    
    missing_files = check_missing_files()
    
    if not missing_files:
        print("✅ ไฟล์ครบถ้วนแล้ว")
        return True
    else:
        print(f"⚠️ ยังมีไฟล์ที่หายไป: {len(missing_files)} ไฟล์")
        return False

def create_new_reports():
    """สร้างรายงานใหม่"""
    print("\n📊 สร้างรายงานสรุปใหม่")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import create_individual_parameter_summaries
        
        create_individual_parameter_summaries()
        print("✅ สร้างรายงานสำเร็จ")
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้างรายงาน: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_loading():
    """ทดสอบการโหลดพารามิเตอร์"""
    print("\n🧪 ทดสอบการโหลดพารามิเตอร์")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import (
            load_scenario_threshold,
            load_scenario_nbars
        )
        
        # ทดสอบกับ symbols ที่มีปัญหา
        test_cases = [
            ('GOLD', 30, 'trend_following'),
            ('GOLD', 30, 'counter_trend'),
            ('USDJPY', 30, 'trend_following'),
            ('USDJPY', 30, 'counter_trend'),
            ('AUDUSD', 60, 'trend_following'),
            ('AUDUSD', 60, 'counter_trend'),
            ('USDJPY', 60, 'trend_following'),
            ('USDJPY', 60, 'counter_trend'),
        ]
        
        success_count = 0
        
        for symbol, timeframe, scenario in test_cases:
            try:
                threshold = load_scenario_threshold(symbol, timeframe, scenario)
                nbars = load_scenario_nbars(symbol, timeframe, scenario)
                
                print(f"✅ {symbol} M{timeframe} {scenario}: T={threshold:.4f}, N={nbars}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ {symbol} M{timeframe} {scenario}: {e}")
        
        print(f"\n📊 ผลการทดสอบ: {success_count}/{len(test_cases)} สำเร็จ")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        return False

def analyze_parameter_differences():
    """วิเคราะห์ความแตกต่างของพารามิเตอร์"""
    print("\n📈 วิเคราะห์ความแตกต่างของพารามิเตอร์")
    print("=" * 50)
    
    try:
        from python_LightGBM_17_Signal import (
            load_scenario_threshold,
            load_scenario_nbars
        )
        
        symbols = ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
        timeframes = [30, 60]
        scenarios = ['trend_following', 'counter_trend']
        
        different_threshold_count = 0
        different_nbars_count = 0
        total_pairs = 0
        
        for symbol in symbols:
            for timeframe in timeframes:
                try:
                    # โหลดพารามิเตอร์ทั้ง 2 scenarios
                    threshold_tf = load_scenario_threshold(symbol, timeframe, 'trend_following')
                    threshold_ct = load_scenario_threshold(symbol, timeframe, 'counter_trend')
                    nbars_tf = load_scenario_nbars(symbol, timeframe, 'trend_following')
                    nbars_ct = load_scenario_nbars(symbol, timeframe, 'counter_trend')
                    
                    # ตรวจสอบความแตกต่าง
                    threshold_diff = abs(threshold_tf - threshold_ct) > 0.001
                    nbars_diff = nbars_tf != nbars_ct
                    
                    if threshold_diff:
                        different_threshold_count += 1
                    if nbars_diff:
                        different_nbars_count += 1
                    
                    total_pairs += 1
                    
                    status_t = "✅" if threshold_diff else "❌"
                    status_n = "✅" if nbars_diff else "❌"
                    
                    print(f"💰 {symbol} M{timeframe}: T={status_t} ({threshold_tf:.3f} vs {threshold_ct:.3f}), N={status_n} ({nbars_tf} vs {nbars_ct})")
                    
                except Exception as e:
                    print(f"❌ {symbol} M{timeframe}: ข้อผิดพลาด ({e})")
        
        print(f"\n📊 สรุปความแตกต่าง:")
        print(f"   Threshold แตกต่าง: {different_threshold_count}/{total_pairs} ({different_threshold_count/total_pairs*100:.1f}%)")
        print(f"   nBars_SL แตกต่าง: {different_nbars_count}/{total_pairs} ({different_nbars_count/total_pairs*100:.1f}%)")
        print(f"   อย่างน้อย 1 ค่าแตกต่าง: {max(different_threshold_count, different_nbars_count)}/{total_pairs}")
        
        return different_threshold_count > 0 or different_nbars_count > 0
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการวิเคราะห์: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔧 แก้ไขปัญหาไฟล์พารามิเตอร์ที่หายไปและสร้างรายงานใหม่")
    print("=" * 80)
    print(f"⏰ เวลาเริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # ขั้นตอนการแก้ไข
    steps = [
        ("ตรวจสอบไฟล์ที่หายไป", check_missing_files),
        ("สร้างไฟล์ที่หายไป", create_missing_files),
        ("ตรวจสอบไฟล์หลังการแก้ไข", verify_all_files),
        ("ทดสอบการโหลดพารามิเตอร์", test_parameter_loading),
        ("สร้างรายงานใหม่", create_new_reports),
        ("วิเคราะห์ความแตกต่างพารามิเตอร์", analyze_parameter_differences),
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            result = step_func()
            results.append((step_name, result))
            status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
            print(f"\n{status} {step_name}")
        except Exception as e:
            print(f"\n❌ {step_name}: เกิดข้อผิดพลาด - {e}")
            results.append((step_name, False))
    
    # สรุปผลการแก้ไข
    print(f"\n{'='*80}")
    print("📊 สรุปผลการแก้ไข:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for step_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
        print(f"   {status} {step_name}")
    
    print(f"\n🎯 ผลรวม: {passed}/{total} ขั้นตอนผ่าน")
    print(f"⏰ เวลาสิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("\n🎉 การแก้ไขสำเร็จสมบูรณ์!")
        print("\n📋 ขั้นตอนถัดไป:")
        print("   1. ตรวจสอบรายงานใหม่ใน LightGBM_Multi/training_summaries/")
        print("   2. อัปเดต MT5 WebRequest Server")
        print("   3. ทดสอบการเทรดจริง")
    elif passed >= 4:
        print("\n✅ การแก้ไขส่วนใหญ่สำเร็จ")
        print("💡 ระบบสามารถใช้งานได้แล้ว")
    else:
        print("\n⚠️ ยังมีปัญหาที่ต้องแก้ไข")

if __name__ == "__main__":
    main()
