#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify MT5 confidence parsing fix
"""

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_strong_sell_data():
    """สร้างข้อมูลที่มี SELL signal แข็งแกร่ง"""
    print("🔍 Creating strong SELL signal data...")
    
    # สร้างข้อมูล 210 bars
    dates = pd.date_range(start='2025-07-24 10:00:00', periods=210, freq='30min')
    
    # สร้าง strong downtrend data
    base_price = 3380.0
    prices = []
    
    for i in range(210):
        # สร้าง strong downtrend
        trend_price = base_price - (i * 1.0)  # ลดลงแรงๆ
        noise = np.random.normal(0, 1.0)  # noise น้อย
        price = trend_price + noise
        prices.append(max(price, 3300.0))  # ไม่ให้ต่ำเกินไป
    
    # สร้าง OHLC data
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        high = price + np.random.uniform(0.5, 2.0)
        low = price - np.random.uniform(0.5, 2.0)
        open_price = price + np.random.uniform(-0.5, 0.5)
        close = price
        
        # สำหรับ bar สุดท้าย ให้เป็น bearish candle
        if i == len(prices) - 1:
            close = price - 2.0  # close ต่ำกว่า open
            low = close - 1.0
        
        data.append({
            'Date': date.strftime('%Y.%m.%d'),
            'Time': date.strftime('%H:%M'),
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close, 2),
            'Volume': np.random.randint(100, 1000)
        })
    
    print(f"✅ Created {len(data)} bars with strong downtrend")
    print(f"   Price range: {min(prices):.2f} - {max(prices):.2f}")
    print(f"   Last bar: Open={data[-1]['Open']}, Close={data[-1]['Close']} (Bearish)")
    
    return data

def test_mt5_integration():
    """ทดสอบการทำงานกับ MT5 จริง"""
    print("\n🚀 Testing MT5 Integration...")
    
    # สร้างข้อมูลทดสอบ
    test_data = create_strong_sell_data()
    
    # ส่งข้อมูลไปยังเซิร์ฟเวอร์
    url = "http://localhost:54321/data"
    
    payload = {
        "symbol": "GOLD#",
        "timeframe_str": "PERIOD_M30",
        "data": test_data
    }
    
    try:
        print("📡 Sending request to Python server...")
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Server response: HTTP 200")
            print("\n📊 Response Analysis:")
            print("=" * 50)
            
            # แสดงข้อมูลสำคัญ
            signal = result.get('signal', 'N/A')
            confidence = result.get('confidence', 0.0)
            threshold = result.get('threshold', 0.0)
            scenario_used = result.get('scenario_used', 'N/A')
            market_condition = result.get('market_condition', 'N/A')
            entry_price = result.get('entry_price', 0.0)
            sl_price = result.get('sl_price', 0.0)
            tp_price = result.get('tp_price', 0.0)
            
            print(f"Signal: {signal}")
            print(f"Confidence: {confidence}")
            print(f"Threshold: {threshold}")
            print(f"Scenario: {scenario_used}")
            print(f"Market: {market_condition}")
            print(f"Entry Price: {entry_price}")
            print(f"SL Price: {sl_price}")
            print(f"TP Price: {tp_price}")
            
            # ตรวจสอบเงื่อนไขการเทรด
            print(f"\n🎯 Trading Analysis:")
            print("=" * 50)
            
            # เงื่อนไขสำหรับการเปิดออเดอร์
            conditions = {
                "Valid Signal": signal in ['BUY', 'SELL'],
                "Positive Confidence": confidence > 0.0,
                "Confidence > Threshold": confidence > threshold,
                "Has Entry Price": entry_price > 0,
                "Has SL Price": sl_price > 0,
                "Has TP Price": tp_price > 0,
                "Scenario Match": (
                    (market_condition == "downtrend" and signal == "SELL" and scenario_used in ["trend_following", "Trend-Following"]) or
                    (market_condition == "uptrend" and signal == "BUY" and scenario_used in ["trend_following", "Trend-Following"]) or
                    (market_condition == "downtrend" and signal == "BUY" and scenario_used in ["counter_trend", "Counter-Trend"]) or
                    (market_condition == "uptrend" and signal == "SELL" and scenario_used in ["counter_trend", "Counter-Trend"])
                )
            }
            
            all_passed = True
            for condition, passed in conditions.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {condition}: {passed}")
                if not passed:
                    all_passed = False
            
            print(f"\n🏆 Final Result:")
            print("=" * 50)
            
            if all_passed:
                print("🎉 SUCCESS: All conditions passed!")
                print("   MT5 should execute the trade")
                print(f"   Expected: {signal} order at {entry_price}")
                print(f"   SL: {sl_price}, TP: {tp_price}")
                
                # คำนวณ risk/reward
                if signal == "SELL":
                    risk = abs(sl_price - entry_price)
                    reward = abs(entry_price - tp_price)
                elif signal == "BUY":
                    risk = abs(entry_price - sl_price)
                    reward = abs(tp_price - entry_price)
                else:
                    risk = reward = 0
                
                if risk > 0:
                    rr_ratio = reward / risk
                    print(f"   Risk/Reward: {rr_ratio:.2f}:1")
                
            else:
                print("❌ FAILED: Some conditions not met")
                print("   MT5 will NOT execute the trade")
                
                # แสดงเหตุผลที่ไม่ผ่าน
                failed_conditions = [cond for cond, passed in conditions.items() if not passed]
                print("   Failed conditions:")
                for cond in failed_conditions:
                    print(f"   - {cond}")
            
            # แสดงข้อมูล JSON ที่ส่งไป MT5
            print(f"\n📤 JSON Sent to MT5:")
            print("=" * 50)
            print(f"Length: {len(json.dumps(result))} characters")
            
            # ตรวจสอบตำแหน่งของ confidence
            json_str = json.dumps(result)
            confidence_positions = []
            start = 0
            while True:
                pos = json_str.find('"confidence":', start)
                if pos == -1:
                    break
                confidence_positions.append(pos)
                start = pos + 1
            
            analysis_pos = json_str.find('"analysis_summary":')
            main_confidence_pos = confidence_positions[0] if confidence_positions else -1
            
            print(f"Main confidence position: {main_confidence_pos}")
            print(f"Analysis summary position: {analysis_pos}")
            print(f"Total confidence keys: {len(confidence_positions)}")
            
            if main_confidence_pos < analysis_pos:
                print("✅ Main confidence comes BEFORE analysis_summary")
                print("   MT5 should parse the correct confidence value")
            else:
                print("❌ Main confidence comes AFTER analysis_summary")
                print("   MT5 might parse wrong confidence value")
            
            return all_passed
            
        else:
            print(f"❌ Server error: HTTP {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing MT5 Confidence Parsing Fix")
    print("=" * 60)
    
    success = test_mt5_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Test PASSED - MT5 should execute trades")
        print("\n💡 Next Steps:")
        print("1. Recompile MT5 EA")
        print("2. Run EA on GOLD# M30 chart")
        print("3. Monitor logs for confidence parsing")
        print("4. Verify trade execution")
    else:
        print("❌ Test FAILED - Issues need to be resolved")
        print("\n🔧 Troubleshooting:")
        print("1. Check Python server is running")
        print("2. Verify data format")
        print("3. Check confidence calculation")
        print("4. Review threshold settings")
