#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify confidence parsing fix in MT5
"""

import json

def test_json_parsing():
    """ทดสอบการ parse JSON ที่มี confidence หลายตำแหน่ง"""
    
    # สร้าง JSON ที่คล้ายกับที่ Python server ส่ง
    test_json = {
        "status": "OK",
        "message": "Latest signal: SELL (0.9556) for bar at 2025.07.24 12:30",
        "signal": "SELL",
        "class": "SELL",
        "confidence": 0.9556,  # ← ค่าหลักที่ต้องการ
        "bar_timestamp": 1753360200.0,
        "signal_bar_timestamp": 1753360200.0,
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_M30",
        "entry_price": 3363.08,
        "sl_price": 3371.38,
        "tp_price": 3338.15,
        "best_entry": 3363.08,
        "nBars_SL": 12,
        "threshold": 0.7000000000000001,
        "time_filters": "Days:[0, 1, 2, 3, 4, 5, 6],Hours:[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]",
        "spread": 25,
        "market_condition": "downtrend",
        "action_type": "sell",
        "scenario_used": "Trend-Following",
        "trend_following_threshold": 0.7000000000000001,
        "trend_following_nbars": 12,
        "counter_trend_threshold": 0.55,
        "counter_trend_nbars": 12,
        "trend_following_buy_confidence": 0.0,
        "trend_following_sell_confidence": 0.9555875438660479,
        "counter_trend_buy_confidence": 0.0,
        "counter_trend_sell_confidence": 0.9447905735004736,
        "analysis_summary": {
            "trend_following": {
                "buy": {
                    "should_trade": False,
                    "confidence": 0.0,  # ← ค่าที่ MT5 เจอผิด
                    "model_info": {
                        "scenario": "trend_following",
                        "action": "buy",
                        "available": True
                    }
                },
                "sell": {
                    "should_trade": True,
                    "confidence": 0.9555875438660479,
                    "model_info": {
                        "scenario": "trend_following",
                        "action": "sell",
                        "available": True
                    }
                }
            },
            "counter_trend": {
                "buy": {
                    "should_trade": False,
                    "confidence": 0.0,
                    "model_info": {
                        "scenario": "counter_trend",
                        "action": "buy",
                        "available": True
                    }
                },
                "sell": {
                    "should_trade": True,
                    "confidence": 0.9447905735004736,
                    "model_info": {
                        "scenario": "counter_trend",
                        "action": "sell",
                        "available": True
                    }
                }
            }
        }
    }
    
    # แปลงเป็น JSON string
    json_str = json.dumps(test_json)
    
    print("🔍 Testing JSON Parsing Logic")
    print("=" * 60)
    print(f"JSON Length: {len(json_str)}")
    
    # หาตำแหน่งของ confidence ทั้งหมด
    confidence_positions = []
    start = 0
    while True:
        pos = json_str.find('"confidence":', start)
        if pos == -1:
            break
        confidence_positions.append(pos)
        start = pos + 1
    
    print(f"Found {len(confidence_positions)} confidence keys at positions: {confidence_positions}")
    
    # หาตำแหน่งของ analysis_summary
    analysis_pos = json_str.find('"analysis_summary":')
    print(f"analysis_summary position: {analysis_pos}")
    
    # แสดงค่า confidence แต่ละตำแหน่ง
    for i, pos in enumerate(confidence_positions):
        # หาค่าที่ตำแหน่งนี้
        value_start = pos + len('"confidence":')
        while value_start < len(json_str) and json_str[value_start] == ' ':
            value_start += 1
        
        value_end = json_str.find(',', value_start)
        if value_end == -1:
            value_end = json_str.find('}', value_start)
        
        if value_end != -1:
            value = json_str[value_start:value_end].strip()
            is_main = pos < analysis_pos if analysis_pos != -1 else True
            status = "✅ MAIN" if is_main else "❌ NESTED"
            print(f"  Position {i+1}: {pos} → value: {value} ({status})")
    
    print("\n🎯 Expected Behavior:")
    print("=" * 60)
    print("✅ MT5 should parse the MAIN confidence: 0.9556")
    print("❌ MT5 should NOT parse nested confidence: 0.0")
    
    print("\n📋 MT5 Parsing Strategy:")
    print("=" * 60)
    print("1. Find analysis_summary position")
    print("2. Search for confidence BEFORE analysis_summary")
    print("3. If found, use that value")
    print("4. If not found, search in full JSON (fallback)")
    
    return json_str

def simulate_mt5_parsing(json_str):
    """จำลองการ parse ของ MT5 ตามโค้ดใหม่"""
    print("\n🔧 Simulating MT5 Parsing Logic:")
    print("=" * 60)
    
    # หาตำแหน่งของ analysis_summary
    analysis_summary_start = json_str.find('"analysis_summary":')
    search_limit = analysis_summary_start if analysis_summary_start != -1 else len(json_str)
    
    print(f"analysis_summary_start: {analysis_summary_start}")
    print(f"search_limit: {search_limit}")
    
    # หาค่า confidence หลัก
    confidence_key_start = json_str.find('"confidence":')
    
    if confidence_key_start >= search_limit:
        print("⚠️ Found confidence inside analysis_summary, searching for main confidence...")
        # หาจากต้นจนถึง analysis_summary
        main_json = json_str[:search_limit]
        confidence_key_start = main_json.find('"confidence":')
        
        if confidence_key_start != -1:
            value_start = confidence_key_start + len('"confidence":')
            while value_start < len(main_json) and main_json[value_start] == ' ':
                value_start += 1
            
            value_end = main_json.find(',', value_start)
            if value_end == -1:
                value_end = main_json.find('}', value_start)
            
            if value_end != -1:
                extracted_value = main_json[value_start:value_end].strip()
                print(f"✅ Found main confidence: '{extracted_value}'")
                return float(extracted_value)
    
    elif confidence_key_start != -1:
        # confidence หลักอยู่ก่อน analysis_summary
        value_start = confidence_key_start + len('"confidence":')
        while value_start < len(json_str) and json_str[value_start] == ' ':
            value_start += 1
        
        value_end = json_str.find(',', value_start)
        if value_end == -1:
            value_end = json_str.find('}', value_start)
        
        if value_end != -1:
            extracted_value = json_str[value_start:value_end].strip()
            print(f"✅ Found main confidence: '{extracted_value}'")
            return float(extracted_value)
    
    print("❌ Failed to parse confidence")
    return 0.0

if __name__ == "__main__":
    print("🔧 Testing Confidence Parsing Fix")
    print("=" * 60)
    
    # ทดสอบการ parse JSON
    json_str = test_json_parsing()
    
    # จำลองการ parse ของ MT5
    parsed_confidence = simulate_mt5_parsing(json_str)
    
    print(f"\n📊 Final Result:")
    print("=" * 60)
    print(f"Parsed Confidence: {parsed_confidence}")
    
    if parsed_confidence == 0.9556:
        print("✅ SUCCESS: Parsed correct main confidence!")
    else:
        print("❌ FAILED: Parsed wrong confidence value!")
        
    print("\n💡 Next Steps:")
    print("=" * 60)
    print("1. Recompile MT5 EA with the new parsing logic")
    print("2. Test with real data from Python server")
    print("3. Verify that MT5 opens trades when confidence > threshold")
