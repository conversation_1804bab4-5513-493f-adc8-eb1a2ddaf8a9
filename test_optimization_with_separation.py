#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการหาค่าที่เหมาะสมด้วยการแยกข้อมูลตาม scenario
"""

import sys
from datetime import datetime

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_optimization_with_separation():
    """ทดสอบการหาค่าที่เหมาะสมด้วยการแยกข้อมูล"""
    print("🚀 ทดสอบการหาค่าที่เหมาะสมด้วยการแยกข้อมูลตาม scenario")
    print("=" * 60)
    print(f"เวลาเริ่มต้น: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        from python_LightGBM_17_Signal import run_multi_model_optimization
        
        # ทดสอบกับ GOLD M30
        symbol = "GOLD"
        timeframe = 30
        
        print(f"\n🔍 กำลังหา optimal parameters สำหรับ {symbol} M{timeframe}")
        print("⚠️ การทดสอบนี้อาจใช้เวลา 2-3 นาที...")
        
        start_time = datetime.now()
        result = run_multi_model_optimization(symbol, timeframe)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        print(f"\n⏱️ เวลาที่ใช้: {duration:.1f} วินาที")
        
        if result:
            print("\n✅ การหาค่าที่เหมาะสมสำเร็จ!")
            print(f"📊 ผลลัพธ์สำหรับ {symbol} M{timeframe}:")
            print(f"   Symbol: {result['symbol']}")
            print(f"   Timeframe: {result['timeframe']}")
            print(f"   Scenarios: {result['scenarios']}")
            print(f"   Validation samples: {result['validation_samples']}")
            
            print(f"\n📋 รายละเอียดพารามิเตอร์:")
            for scenario in result['scenarios']:
                threshold = result['optimal_thresholds'].get(scenario, 'N/A')
                nbars = result['optimal_nbars'].get(scenario, 'N/A')
                print(f"   {scenario}:")
                print(f"     Threshold: {threshold}")
                print(f"     nBars_SL: {nbars}")
            
            # ตรวจสอบว่าค่าแตกต่างกันหรือไม่
            thresholds = list(result['optimal_thresholds'].values())
            nbars_values = list(result['optimal_nbars'].values())
            
            threshold_diff = len(set(thresholds)) > 1
            nbars_diff = len(set(nbars_values)) > 1
            
            print(f"\n🔍 การวิเคราะห์ความแตกต่าง:")
            print(f"   Threshold values: {thresholds}")
            print(f"   nBars_SL values: {nbars_values}")
            print(f"   Threshold แตกต่างกัน: {'✅ ใช่' if threshold_diff else '❌ ไม่'}")
            print(f"   nBars_SL แตกต่างกัน: {'✅ ใช่' if nbars_diff else '❌ ไม่'}")
            
            if threshold_diff or nbars_diff:
                print("\n🎉 สำเร็จ! ค่าพารามิเตอร์แตกต่างกันระหว่าง scenarios แล้ว")
                return True
            else:
                print("\n⚠️ ค่าพารามิเตอร์ยังคงเหมือนกัน - อาจต้องปรับปรุงเพิ่มเติม")
                return False
        else:
            print("❌ การหาค่าที่เหมาะสมไม่สำเร็จ")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compare_before_after():
    """เปรียบเทียบผลลัพธ์ก่อนและหลังการปรับปรุง"""
    print("\n" + "=" * 60)
    print("📊 เปรียบเทียบผลลัพธ์ก่อนและหลังการปรับปรุง")
    
    try:
        from python_LightGBM_17_Signal import (
            load_scenario_threshold,
            load_scenario_nbars
        )
        
        symbols = ['GOLD', 'AUDUSD', 'EURUSD']
        timeframes = [30, 60]
        scenarios = ['trend_following', 'counter_trend']
        
        print(f"\n📋 ค่าพารามิเตอร์ปัจจุบันในไฟล์:")
        
        for symbol in symbols:
            print(f"\n💰 {symbol}:")
            for timeframe in timeframes:
                print(f"  🕐 M{timeframe}:")
                
                for scenario in scenarios:
                    try:
                        threshold = load_scenario_threshold(symbol, timeframe, scenario)
                        nbars = load_scenario_nbars(symbol, timeframe, scenario)
                        print(f"    📊 {scenario}: threshold={threshold:.4f}, nBars_SL={nbars}")
                    except Exception as e:
                        print(f"    ❌ {scenario}: ไม่สามารถโหลดได้ ({e})")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการเปรียบเทียบ: {e}")
        return False

def test_quick_validation():
    """ทดสอบการตรวจสอบอย่างรวดเร็ว"""
    print("\n" + "=" * 60)
    print("⚡ การทดสอบการตรวจสอบอย่างรวดเร็ว")
    
    try:
        from python_LightGBM_17_Signal import (
            filter_data_by_scenario,
            get_default_threshold_by_scenario,
            get_default_nbars_by_scenario,
            load_validation_data_for_optimization
        )
        
        # ทดสอบการโหลดข้อมูล
        symbol = "GOLD"
        timeframe = 30
        
        print(f"\n🔍 โหลดข้อมูล validation สำหรับ {symbol} M{timeframe}")
        val_df = load_validation_data_for_optimization(symbol, timeframe)
        
        if val_df is None:
            print("❌ ไม่สามารถโหลดข้อมูลได้")
            return False
        
        print(f"✅ โหลดข้อมูลสำเร็จ: {val_df.shape}")
        
        # ทดสอบการแยกข้อมูล
        scenarios = ['trend_following', 'counter_trend']
        filtered_data = {}
        
        for scenario in scenarios:
            filtered_df = filter_data_by_scenario(val_df, scenario)
            filtered_data[scenario] = len(filtered_df)
            print(f"📊 {scenario}: {len(filtered_df)}/{len(val_df)} samples ({len(filtered_df)/len(val_df)*100:.1f}%)")
        
        # ตรวจสอบว่าข้อมูลแตกต่างกัน
        if filtered_data['trend_following'] != filtered_data['counter_trend']:
            print("✅ การแยกข้อมูลทำงานถูกต้อง - ได้ข้อมูลที่แตกต่างกัน")
        else:
            print("⚠️ การแยกข้อมูลอาจไม่ได้ผล - ข้อมูลยังคล้ายกันมาก")
        
        # ทดสอบค่า default
        print(f"\n🎯 ทดสอบค่า default:")
        for scenario in scenarios:
            threshold = get_default_threshold_by_scenario(scenario, symbol)
            nbars = get_default_nbars_by_scenario(scenario, symbol, timeframe)
            print(f"   {scenario}: threshold={threshold:.3f}, nBars_SL={nbars}")
        
        return True
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """ฟังก์ชันหลัก"""
    print("🔍 เริ่มการทดสอบการหาค่าที่เหมาะสมด้วยการแยกข้อมูล")
    print("=" * 80)
    
    # ทดสอบแต่ละส่วน
    tests = [
        ("การตรวจสอบอย่างรวดเร็ว", test_quick_validation),
        ("เปรียบเทียบผลลัพธ์", test_compare_before_after),
        ("การหาค่าที่เหมาะสมแบบใหม่", test_optimization_with_separation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
            print(f"\n{status} {test_name}")
        except Exception as e:
            print(f"\n❌ {test_name}: เกิดข้อผิดพลาด - {e}")
            results.append((test_name, False))
    
    # สรุปผลการทดสอบ
    print(f"\n{'='*80}")
    print("📊 สรุปผลการทดสอบ:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ ผ่าน" if result else "❌ ไม่ผ่าน"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 ผลรวม: {passed}/{total} การทดสอบผ่าน")
    print(f"⏱️ เวลาสิ้นสุด: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("🎉 การทดสอบทั้งหมดผ่าน! ระบบพร้อมใช้งาน")
        print("\n📋 ขั้นตอนถัดไป:")
        print("   1. รันการหาค่าที่เหมาะสมสำหรับ symbols อื่นๆ")
        print("   2. ตรวจสอบการใช้งานใน MT5 WebRequest Server")
        print("   3. ทดสอบการเทรดจริงด้วยพารามิเตอร์ใหม่")
    else:
        print("⚠️ มีการทดสอบที่ไม่ผ่าน กรุณาตรวจสอบและแก้ไข")

if __name__ == "__main__":
    main()
