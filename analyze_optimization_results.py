#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
วิเคราะห์ผลลัพธ์การหาค่าที่เหมาะสม
"""

import json
import sys
import os
from datetime import datetime

def analyze_optimization_results():
    """วิเคราะห์ผลลัพธ์การหาค่าที่เหมาะสม"""
    print("📊 วิเคราะห์ผลลัพธ์การหาค่าที่เหมาะสม")
    print("=" * 60)
    
    # โหลดผลลัพธ์
    results_file = "LightGBM_Multi/training_summaries/optimization_results_20250722_155245.json"
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print(f"📁 โหลดผลลัพธ์จาก: {results_file}")
        
        # สถิติรวม
        total_combinations = 0
        successful = 0
        threshold_different_count = 0
        nbars_different_count = 0
        both_different_count = 0
        
        print("\n📊 ผลลัพธ์แต่ละ Symbol:")
        print("=" * 60)
        
        for symbol in sorted(results.keys()):
            print(f"\n💰 {symbol}:")
            
            for timeframe in sorted(results[symbol].keys(), key=int):
                result = results[symbol][timeframe]
                total_combinations += 1
                
                if result['success']:
                    successful += 1
                    
                    threshold_diff = result['threshold_different']
                    nbars_diff = result['nbars_different']
                    
                    if threshold_diff:
                        threshold_different_count += 1
                    if nbars_diff:
                        nbars_different_count += 1
                    if threshold_diff and nbars_diff:
                        both_different_count += 1
                    
                    # แสดงผลลัพธ์
                    thresholds = result['thresholds']
                    nbars = result['nbars']
                    duration = result['duration']
                    
                    status_t = "✅" if threshold_diff else "❌"
                    status_n = "✅" if nbars_diff else "❌"
                    
                    print(f"  🕐 M{timeframe}: {status_t}T {status_n}N ({duration:.1f}s)")
                    print(f"     TF: T={thresholds[0]:.3f}, N={nbars[0]}")
                    print(f"     CT: T={thresholds[1]:.3f}, N={nbars[1]}")
                    
                    if threshold_diff and nbars_diff:
                        print(f"     🎉 ทั้งคู่แตกต่าง!")
                    elif threshold_diff:
                        print(f"     ✅ Threshold แตกต่าง")
                    elif nbars_diff:
                        print(f"     ✅ nBars_SL แตกต่าง")
                    else:
                        print(f"     ⚠️ เหมือนกันทั้งคู่")
                else:
                    print(f"  🕐 M{timeframe}: ❌ ไม่สำเร็จ")
        
        # สรุปสถิติ
        print(f"\n{'='*60}")
        print("📈 สถิติรวม:")
        print(f"   📊 ทั้งหมด: {total_combinations} combinations")
        print(f"   ✅ สำเร็จ: {successful}/{total_combinations} ({successful/total_combinations*100:.1f}%)")
        print(f"   🎯 Threshold แตกต่าง: {threshold_different_count}/{successful} ({threshold_different_count/successful*100:.1f}%)")
        print(f"   📏 nBars_SL แตกต่าง: {nbars_different_count}/{successful} ({nbars_different_count/successful*100:.1f}%)")
        print(f"   🎉 ทั้งคู่แตกต่าง: {both_different_count}/{successful} ({both_different_count/successful*100:.1f}%)")
        print(f"   📋 อย่างน้อย 1 ค่าแตกต่าง: {max(threshold_different_count, nbars_different_count)}/{successful}")
        
        # วิเคราะห์ความแตกต่าง
        print(f"\n📊 วิเคราะห์ความแตกต่าง:")
        
        # หา symbols ที่มีความแตกต่างสูง
        high_diff_symbols = []
        for symbol in results.keys():
            symbol_diff_count = 0
            symbol_total = 0
            
            for timeframe in results[symbol].keys():
                result = results[symbol][timeframe]
                if result['success']:
                    symbol_total += 1
                    if result['threshold_different'] or result['nbars_different']:
                        symbol_diff_count += 1
            
            if symbol_total > 0:
                diff_rate = symbol_diff_count / symbol_total
                if diff_rate >= 0.5:  # 50% ขึ้นไป
                    high_diff_symbols.append((symbol, diff_rate, symbol_diff_count, symbol_total))
        
        if high_diff_symbols:
            print(f"   🏆 Symbols ที่มีความแตกต่างสูง:")
            for symbol, rate, diff_count, total in sorted(high_diff_symbols, key=lambda x: x[1], reverse=True):
                print(f"     💰 {symbol}: {diff_count}/{total} ({rate*100:.1f}%)")
        
        # ตรวจสอบ timeframes
        m30_diff = 0
        m30_total = 0
        m60_diff = 0
        m60_total = 0
        
        for symbol in results.keys():
            for timeframe in results[symbol].keys():
                result = results[symbol][timeframe]
                if result['success']:
                    has_diff = result['threshold_different'] or result['nbars_different']
                    
                    if timeframe == '30':
                        m30_total += 1
                        if has_diff:
                            m30_diff += 1
                    elif timeframe == '60':
                        m60_total += 1
                        if has_diff:
                            m60_diff += 1
        
        print(f"\n   📊 ตาม Timeframe:")
        if m30_total > 0:
            print(f"     🕐 M30: {m30_diff}/{m30_total} ({m30_diff/m30_total*100:.1f}%)")
        if m60_total > 0:
            print(f"     🕐 M60: {m60_diff}/{m60_total} ({m60_diff/m60_total*100:.1f}%)")
        
        # สรุปความสำเร็จ
        success_rate = successful / total_combinations
        diff_rate = max(threshold_different_count, nbars_different_count) / successful if successful > 0 else 0
        
        print(f"\n🎯 สรุปความสำเร็จ:")
        if success_rate >= 0.9 and diff_rate >= 0.5:
            print("   🎉 การแก้ไขสำเร็จสมบูรณ์!")
            print("   💡 ระบบสามารถสร้างค่าพารามิเตอร์ที่แตกต่างกันได้แล้ว")
        elif success_rate >= 0.8:
            print("   ✅ การแก้ไขส่วนใหญ่สำเร็จ")
            print("   💡 ระบบทำงานได้ดีแล้ว")
        else:
            print("   ⚠️ ยังมีปัญหาที่ต้องแก้ไข")
        
        return {
            'total': total_combinations,
            'successful': successful,
            'threshold_different': threshold_different_count,
            'nbars_different': nbars_different_count,
            'both_different': both_different_count,
            'success_rate': success_rate,
            'diff_rate': diff_rate
        }
        
    except FileNotFoundError:
        print(f"❌ ไม่พบไฟล์: {results_file}")
        return None
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return None

def main():
    """ฟังก์ชันหลัก"""
    print("🔍 วิเคราะห์ผลลัพธ์การหาค่าที่เหมาะสม")
    print("=" * 70)
    print(f"⏰ เวลา: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = analyze_optimization_results()
    
    if results:
        print(f"\n📋 ขั้นตอนถัดไป:")
        if results['success_rate'] >= 0.9 and results['diff_rate'] >= 0.5:
            print("   1. ✅ ระบบพร้อมใช้งาน")
            print("   2. อัปเดต MT5 WebRequest Server")
            print("   3. ทดสอบการเทรดจริง")
            print("   4. Monitor การทำงานของ multi-model architecture")
        else:
            print("   1. ตรวจสอบ symbols ที่ยังมีปัญหา")
            print("   2. ปรับปรุงอัลกอริทึมการหาค่าที่เหมาะสม")
            print("   3. เพิ่ม missing features")

if __name__ == "__main__":
    main()
