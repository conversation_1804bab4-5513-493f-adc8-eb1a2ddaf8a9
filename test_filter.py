#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Filter Script
สคริปต์ทดสอบการกรองข้อมูลสำหรับระบบเปรียบเทียบ Entry Conditions
"""

import sys
import os

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_filter_logic():
    """ทดสอบตรรกะการกรองข้อมูล"""
    print("🔍 ทดสอบตรรกะการกรองข้อมูล")
    print("=" * 50)
    
    # ข้อมูลทดสอบ (จำลอง test_groups)
    test_groups = {
        "M30": [
            "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/EURGBP_M30_FIXED.csv",
            "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
            "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
            "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
        ],
        "M60": [
            "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/EURGBP_H1_FIXED.csv",
            "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
            "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
            "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
        ]
    }
    
    # ทดสอบการกรองต่างๆ
    test_cases = [
        {
            "name": "กรองเฉพาะ GOLD",
            "symbols": ["GOLD"],
            "timeframes": None
        },
        {
            "name": "กรองเฉพาะ M30",
            "symbols": None,
            "timeframes": [30]
        },
        {
            "name": "กรอง GOLD M30",
            "symbols": ["GOLD"],
            "timeframes": [30]
        },
        {
            "name": "กรอง GOLD, EURUSD M30, M60",
            "symbols": ["GOLD", "EURUSD"],
            "timeframes": [30, 60]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 ทดสอบที่ {i}: {test_case['name']}")
        print(f"   Symbols: {test_case['symbols']}")
        print(f"   Timeframes: {test_case['timeframes']}")
        
        filtered_groups = filter_test_groups(test_groups, test_case['symbols'], test_case['timeframes'])
        
        print(f"   ผลลัพธ์:")
        for group_name, files in filtered_groups.items():
            print(f"     {group_name}: {len(files)} ไฟล์")
            for file_path in files:
                file_name = os.path.basename(file_path)
                print(f"       - {file_name}")
        
        if not filtered_groups:
            print(f"     ❌ ไม่มีไฟล์ที่ตรงเงื่อนไข")

def filter_test_groups(test_groups, filter_symbols=None, filter_timeframes=None):
    """ฟังก์ชันกรองข้อมูล (จำลองจากโค้ดหลัก)"""
    filtered_test_groups = {}
    
    for group_name, group_files in test_groups.items():
        # ตรวจสอบ timeframe
        if filter_timeframes:
            timeframe_num = int(group_name[1:])  # M30 -> 30, M60 -> 60
            if timeframe_num not in filter_timeframes:
                continue
        
        # กรองไฟล์ตาม symbols
        filtered_files = []
        for file_path in group_files:
            file_name = os.path.basename(file_path)
            
            # ดึง symbol จากชื่อไฟล์ (เช่น GOLD_M30_FIXED.csv -> GOLD)
            symbol = file_name.split('_')[0]
            
            if filter_symbols and symbol not in filter_symbols:
                continue
            
            filtered_files.append(file_path)
        
        if filtered_files:
            filtered_test_groups[group_name] = filtered_files
    
    return filtered_test_groups

def test_symbol_extraction():
    """ทดสอบการดึง symbol จากชื่อไฟล์"""
    print("\n🔍 ทดสอบการดึง symbol จากชื่อไฟล์")
    print("=" * 50)
    
    test_files = [
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv"
    ]
    
    for file_path in test_files:
        file_name = os.path.basename(file_path)
        symbol = file_name.split('_')[0]
        print(f"   {file_name} -> Symbol: {symbol}")

def test_timeframe_extraction():
    """ทดสอบการดึง timeframe จากชื่อกลุ่ม"""
    print("\n🔍 ทดสอบการดึง timeframe จากชื่อกลุ่ม")
    print("=" * 50)
    
    test_groups = ["M30", "M60", "M15", "M240"]
    
    for group_name in test_groups:
        timeframe_num = int(group_name[1:])  # M30 -> 30
        print(f"   {group_name} -> Timeframe: {timeframe_num}")

def test_command_line_simulation():
    """จำลองการทำงานของ command line"""
    print("\n🔍 จำลองการทำงานของ command line")
    print("=" * 50)
    
    # จำลองคำสั่ง: python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30
    simulated_args = {
        "symbols": ["GOLD"],
        "timeframes": [30]
    }
    
    print(f"จำลองคำสั่ง: --symbols {simulated_args['symbols']} --timeframes {simulated_args['timeframes']}")
    
    # ทดสอบการส่งพารามิเตอร์
    print(f"พารามิเตอร์ที่จะส่งไป run_main_analysis:")
    print(f"   filter_symbols = {simulated_args['symbols']}")
    print(f"   filter_timeframes = {simulated_args['timeframes']}")

def check_csv_files():
    """ตรวจสอบไฟล์ CSV ที่มีอยู่จริง"""
    print("\n🔍 ตรวจสอบไฟล์ CSV ที่มีอยู่จริง")
    print("=" * 50)
    
    csv_folder = "CSV_Files_Fixed"
    
    if not os.path.exists(csv_folder):
        print(f"❌ ไม่พบโฟลเดอร์: {csv_folder}")
        return
    
    files = os.listdir(csv_folder)
    csv_files = [f for f in files if f.endswith('.csv')]
    
    print(f"📁 พบไฟล์ CSV: {len(csv_files)} ไฟล์")
    
    symbols = set()
    timeframes = set()
    
    for file_name in sorted(csv_files):
        parts = file_name.split('_')
        if len(parts) >= 2:
            symbol = parts[0]
            timeframe_part = parts[1]
            
            symbols.add(symbol)
            timeframes.add(timeframe_part)
            
            print(f"   {file_name} -> Symbol: {symbol}, Timeframe: {timeframe_part}")
    
    print(f"\n📊 สรุป:")
    print(f"   Symbols ที่มี: {sorted(symbols)}")
    print(f"   Timeframes ที่มี: {sorted(timeframes)}")

def main():
    print("🧪 Test Filter Script")
    print("=" * 50)
    
    # รันการทดสอบทั้งหมด
    test_symbol_extraction()
    test_timeframe_extraction()
    test_filter_logic()
    test_command_line_simulation()
    check_csv_files()
    
    print("\n✅ การทดสอบเสร็จสิ้น")

if __name__ == "__main__":
    main()
