# 🔧 การแก้ไขเงื่อนไขการเข้าใน Server ให้ตรงกับ Training Model

## 🎯 ปัญหาที่พบ

### 1. **ไม่มีการแยก Scenario-Specific Conditions**
- Server ใช้เงื่อนไขเดียวกันสำหรับทั้ง `trend_following` และ `counter_trend`
- Training model แยกเงื่อนไขตาม scenario อย่างชัดเจน

### 2. **เงื่อนไข EMA200 หายไป**
- **Training**: `trend_following` ต้อง `close > EMA200`, `counter_trend` ต้อง `close < EMA200`
- **Server**: ไม่มีการตรวจสอบ EMA200 เลย

### 3. **RSI Conditions ไม่ตรงกัน**
- **Training trend_following BUY**: `rsi > 0.8*rsi_level_in AND rsi < 75`
- **Training counter_trend BUY**: `rsi < 35`
- **Server**: ใช้ `rsi > 0.8*rsi_level_in` เดียวกันทั้งหมด

### 4. **Volume Multiplier ไม่ตรงกัน**
- **Training**: trend_following ใช้ `0.5`, counter_trend ใช้ `0.8`
- **Server**: ใช้ `0.5` เดียวกันทั้งหมด

### 5. **Ratio Conditions ไม่ตรงกัน**
- **Training**: ใช้ `1.5`
- **Server**: ใช้ `0.8`

## ✅ การแก้ไขที่ทำ

### 1. **เพิ่ม Scenario-Based Conditions**
```python
# กำหนดเงื่อนไขตาม scenario ที่ใช้ (ให้ตรงกับ training model)
if USE_MULTI_MODEL_ARCHITECTURE and 'scenario_name' in locals():
    if scenario_name == "trend_following":
        # === Trend Following Conditions ===
        if predicted_signal in ["BUY", "STRONG_BUY"]:
            tech_signal_buy_conditions = {
                'Close > Open (Prev Bar)': close > latest_features_dict_all_i2.get('Open', float('inf')),
                'Close > EMA200 (Trend Following)': close > ema200,  # ✅ เพิ่มเงื่อนไข EMA200
                'RSI > input_rsi_level_in * 0.8 AND < 75': rsi_signal > (input_rsi_level_in * 0.8) and rsi_signal < 75,  # ✅ เพิ่มขอบเขตบน
                'MACD_signal == 1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == 1.0,
                'Volume > Volume_MA20 * 0.50 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.50,  # ✅ ใช้ 0.5 สำหรับ trend_following
                'prev_pullback_buy > input_pull_back * 0.5': latest_features_dict_all_i2.get('PullBack_Up', 0.0) > (input_pull_back * 0.5),
                'prev_ratio_buy > input_take_profit * 1.5': latest_features_dict_all_i2.get('Ratio_Buy', 0.0) > (input_take_profit * 1.5)  # ✅ ใช้ 1.5 ตาม training
            }
```

### 2. **Trend Following SELL (สวนเทรน)**
```python
else:
    # Trend Following SELL: close > EMA200 แต่ขาย (สวนเทรน)
    tech_signal_sell_conditions = {
        'Close < Open (Prev Bar)': close < latest_features_dict_all_i2.get('Open', -float('inf')),
        'Close > EMA200 (Still Above Trend)': close > ema200,  # ✅ ยังอยู่เหนือ EMA200
        'RSI > 65 (Overbought)': rsi_signal > 65,  # ✅ ใช้ 65 ตาม training
        'MACD_signal == -1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == -1.0,
        'Volume > Volume_MA20 * 0.80 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.80,  # ✅ ใช้ 0.8 สำหรับ SELL
        'prev_pullback_sell > input_pull_back * 0.8': latest_features_dict_all_i2.get('PullBack_Down', 0.0) > (input_pull_back * 0.8),
        'prev_ratio_sell > input_take_profit * 1.5': latest_features_dict_all_i2.get('Ratio_Sell', 0.0) > (input_take_profit * 1.5)
    }
```

### 3. **Counter Trend BUY (สวนเทรน)**
```python
elif scenario_name == "counter_trend":
    if predicted_signal in ["BUY", "STRONG_BUY"]:
        # Counter Trend BUY: close < EMA200 แต่ซื้อ (สวนเทรน)
        tech_signal_buy_conditions = {
            'Close > Open (Prev Bar)': close > latest_features_dict_all_i2.get('Open', float('inf')),
            'Close < EMA200 (Below Trend)': close < ema200,  # ✅ อยู่ใต้ EMA200
            'RSI < 35 (Oversold)': rsi_signal < 35,  # ✅ ใช้ 35 ตาม training
            'MACD_signal == 1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == 1.0,
            'Volume > Volume_MA20 * 0.80 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.80,  # ✅ ใช้ 0.8 สำหรับ counter_trend
            'prev_pullback_buy > input_pull_back * 0.8': latest_features_dict_all_i2.get('PullBack_Up', 0.0) > (input_pull_back * 0.8),
            'prev_ratio_buy > input_take_profit * 1.5': latest_features_dict_all_i2.get('Ratio_Buy', 0.0) > (input_take_profit * 1.5)
        }
```

### 4. **Counter Trend SELL (ตามเทรน)**
```python
else:
    # Counter Trend SELL: close < EMA200 (ตามเทรน)
    tech_signal_sell_conditions = {
        'Close < Open (Prev Bar)': close < latest_features_dict_all_i2.get('Open', -float('inf')),
        'Close < EMA200 (Following Trend)': close < ema200,  # ✅ ตามเทรนด์ลง
        'RSI < (100-input_rsi_level_in * 0.8) AND > 25': rsi_signal < (100 - input_rsi_level_in * 0.8) and rsi_signal > 25,  # ✅ เพิ่มขอบเขตล่าง
        'MACD_signal == -1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == -1.0,
        'Volume > Volume_MA20 * 0.50 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.50,  # ✅ ใช้ 0.5 สำหรับ SELL
        'prev_pullback_sell > input_pull_back * 0.5': latest_features_dict_all_i2.get('PullBack_Down', 0.0) > (input_pull_back * 0.5),
        'prev_ratio_sell > input_take_profit * 1.5': latest_features_dict_all_i2.get('Ratio_Sell', 0.0) > (input_take_profit * 1.5)
    }
```

### 5. **Fallback สำหรับ Single Model**
```python
else:
    # === Fallback: เงื่อนไขเดิมสำหรับ Single Model หรือกรณีไม่มี scenario ===
    tech_signal_buy_conditions = {
        'Close > Open (Prev Bar)': close > latest_features_dict_all_i2.get('Open', float('inf')),
        'RSI_signal > input_rsi_level_in * 0.8 (Prev Bar)': rsi_signal > (input_rsi_level_in * 0.8),
        'MACD_signal == 1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == 1.0,
        'Volume > Volume_MA20 * 0.50 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.50,
        'prev_pullback_buy > input_pull_back * 0.5': latest_features_dict_all_i2.get('PullBack_Up', 0.0) > (input_pull_back * 0.5),
        'prev_ratio_buy > input_take_profit * 0.8': latest_features_dict_all_i2.get('Ratio_Buy', 0.0) > (input_take_profit * 0.8)
    }
```

### 6. **ปรับปรุงการแสดงผล**
```python
# แสดงเงื่อนไขทางเทคนิคเฉพาะเมื่อเปิดใช้งาน
if ENABLE_TECHNICAL_CONDITIONS:
    if predicted_signal in ["BUY", "STRONG_BUY"] and tech_signal_buy_conditions:
        print(f"🟢 BUY Technical Conditions ({scenario_name if 'scenario_name' in locals() else 'default'}):")
        for condition, result in tech_signal_buy_conditions.items():
            status = "✅" if result else "❌"
            print(f"   {status} {condition}: {result}")
        print(f"🟢 Overall BUY Tech Signal: {tech_signal_buy}")
```

## 🎯 ผลลัพธ์ที่คาดหวัง

1. **ความสอดคล้องกับ Training**: เงื่อนไขการเข้าใน Server จะตรงกับ Training Model 100%
2. **Scenario-Specific Logic**: แยกเงื่อนไขตาม `trend_following` และ `counter_trend` อย่างชัดเจน
3. **EMA200 Integration**: ใช้ EMA200 เป็นตัวกำหนด scenario และเงื่อนไขการเข้า
4. **Accurate Technical Analysis**: RSI, Volume, Ratio conditions ตรงกับที่ใช้ใน training
5. **Better Signal Quality**: สัญญาณที่ได้จะมีคุณภาพสูงขึ้นเพราะเงื่อนไขตรงกับที่โมเดลเรียนรู้

## 🔄 การทดสอบที่แนะนำ

1. **ทดสอบ Trend Following Scenarios**: ตรวจสอบว่าเมื่อ `close > EMA200` จะใช้เงื่อนไข trend_following
2. **ทดสอบ Counter Trend Scenarios**: ตรวจสอบว่าเมื่อ `close < EMA200` จะใช้เงื่อนไข counter_trend
3. **ตรวจสอบ RSI Boundaries**: ทดสอบ RSI < 35 สำหรับ counter_trend BUY และ RSI > 65 สำหรับ trend_following SELL
4. **ทดสอบ Volume Multipliers**: ตรวจสอบการใช้ 0.5 และ 0.8 ตาม scenario
5. **ทดสอบ Ratio Values**: ตรวจสอบการใช้ 1.5 แทน 0.8

## 📝 หมายเหตุ

- การแก้ไขนี้จะทำให้ Server มีความสอดคล้องกับ Training Model มากขึ้น
- ควรทดสอบกับข้อมูลจริงเพื่อยืนยันว่าสัญญาณที่ได้มีคุณภาพดีขึ้น
- อาจต้องปรับ threshold หรือพารามิเตอร์อื่นๆ หลังจากการทดสอบ
