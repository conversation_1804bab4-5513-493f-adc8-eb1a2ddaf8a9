#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบระบบสรุปผลการเทรนสำหรับ Multi-Model Architecture
Test Multi-Model Training Summary System

วิธีใช้:
1. รันไฟล์นี้เพื่อทดสอบการเทรน Multi-Model
2. ตรวจสอบว่าระบบสรุปทำงานอัตโนมัติหรือไม่
3. ดูผลลัพธ์ในโฟลเดอร์ training_summaries
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_sample_data_for_multi_model():
    """สร้างข้อมูลตัวอย่างสำหรับทดสอบ Multi-Model"""
    
    print("🔧 สร้างข้อมูลตัวอย่างสำหรับ Multi-Model...")
    
    # สร้างข้อมูล OHLC
    dates = pd.date_range('2024-01-01', periods=1000, freq='30T')
    
    # สร้างข้อมูลราคา
    np.random.seed(42)
    base_price = 1800
    price_changes = np.random.normal(0, 5, len(dates))
    prices = base_price + np.cumsum(price_changes)
    
    # สร้าง OHLC
    df = pd.DataFrame({
        'Date': dates,
        'Open': prices + np.random.normal(0, 1, len(dates)),
        'High': prices + np.abs(np.random.normal(2, 1, len(dates))),
        'Low': prices - np.abs(np.random.normal(2, 1, len(dates))),
        'Close': prices,
        'Volume': np.random.randint(1000, 10000, len(dates))
    })
    
    # เพิ่ม indicators พื้นฐาน (ใช้ชื่อที่ระบบต้องการ)
    df['EMA200'] = df['Close'].ewm(span=200).mean()  # ใช้ EMA200 แทน EMA_200
    df['RSI'] = 50 + np.random.normal(0, 15, len(dates))  # Mock RSI
    df['MACD'] = np.random.normal(0, 2, len(dates))  # Mock MACD

    # เพิ่ม indicators อื่นๆ ที่ระบบอาจต้องการ
    df['SMA20'] = df['Close'].rolling(20).mean()
    df['SMA50'] = df['Close'].rolling(50).mean()
    df['BB_Upper'] = df['Close'].rolling(20).mean() + 2 * df['Close'].rolling(20).std()
    df['BB_Lower'] = df['Close'].rolling(20).mean() - 2 * df['Close'].rolling(20).std()
    df['ATR'] = np.abs(df['High'] - df['Low'])  # Mock ATR
    
    # เพิ่ม features เพิ่มเติม
    for i in range(1, 21):  # เพิ่ม 20 features
        df[f'Feature_{i}'] = np.random.normal(0, 1, len(dates))
    
    # สร้าง Target
    df['Target'] = np.random.choice([0, 1], len(dates), p=[0.6, 0.4])
    df['Target_Multiclass'] = np.random.choice([0, 1, 2, 3, 4], len(dates), 
                                              p=[0.2, 0.2, 0.2, 0.2, 0.2])
    
    # ลบ NaN
    df = df.dropna()
    
    print(f"✅ สร้างข้อมูลตัวอย่าง: {len(df)} rows, {len(df.columns)} columns")
    
    return df

def test_multi_model_training():
    """ทดสอบการเทรน Multi-Model และระบบสรุป"""
    
    print("🧪 เริ่มทดสอบการเทรน Multi-Model")
    print("=" * 60)
    
    try:
        # Import ฟังก์ชันจากไฟล์หลัก
        from python_LightGBM_17_Signal import (
            train_all_scenario_models,
            view_training_summary_reports,
            compare_training_progress,
            USE_MULTI_MODEL_ARCHITECTURE
        )
        print("✅ Import ฟังก์ชันสำเร็จ")
        
        # ตรวจสอบการตั้งค่า
        print(f"🔍 USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
        
        if not USE_MULTI_MODEL_ARCHITECTURE:
            print("⚠️ USE_MULTI_MODEL_ARCHITECTURE = False")
            print("💡 กรุณาแก้ไขเป็น True ในไฟล์หลักและลองใหม่")
            return False
        
    except ImportError as e:
        print(f"❌ ไม่สามารถ import ฟังก์ชันได้: {e}")
        return False
    
    # สร้างข้อมูลตัวอย่าง
    df = create_sample_data_for_multi_model()
    
    # ทดสอบการเทรน Multi-Model
    print("\n1️⃣ ทดสอบการเทรน Multi-Model...")
    try:
        symbol = "GOLD"
        timeframe = 30
        
        print(f"🚀 เริ่มเทรน {symbol} M{timeframe}")
        
        # เรียกใช้ฟังก์ชันเทรน Multi-Model
        results = train_all_scenario_models(
            df=df,
            symbol=symbol,
            timeframe=timeframe,
            target_column='Target_Multiclass',
            output_folder=None
        )
        
        if results and len(results) > 0:
            print(f"✅ เทรน Multi-Model สำเร็จ: {len(results)} scenarios")
            
            # แสดงผลลัพธ์
            for scenario_name, result in results.items():
                if result:
                    print(f"   📊 {scenario_name}:")
                    print(f"      - Keys: {list(result.keys())}")
                    if 'trade_df' in result and result['trade_df'] is not None:
                        print(f"      - Trade DF: {len(result['trade_df'])} rows")
                    else:
                        print(f"      - Trade DF: None หรือว่าง")
        else:
            print("❌ การเทรน Multi-Model ล้มเหลว")
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการเทรน Multi-Model: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # ทดสอบการดูรายงานสรุป
    print("\n2️⃣ ทดสอบการดูรายงานสรุป...")
    try:
        print("--- ดูรายงานทั้งหมด ---")
        view_training_summary_reports(show_details=False)
        
        print(f"\n--- ดูรายงานเฉพาะ {symbol} ---")
        view_training_summary_reports(symbol=symbol, show_details=True)
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการดูรายงาน: {e}")
        # ไม่ return False เพราะอาจเป็นเพียงการแสดงผล
    
    # ทดสอบการเปรียบเทียบความก้าวหน้า
    print("\n3️⃣ ทดสอบการเปรียบเทียบความก้าวหน้า...")
    try:
        compare_training_progress(symbol, timeframe, show_chart=False)
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการเปรียบเทียบ: {e}")
        # ไม่ return False เพราะอาจเป็นเพียงการแสดงผล
    
    # ตรวจสอบไฟล์ที่ถูกสร้าง
    print("\n4️⃣ ตรวจสอบไฟล์ที่ถูกสร้าง...")
    try:
        from python_LightGBM_17_Signal import test_folder
        summary_folder = f"{test_folder}/training_summaries"
        
        if os.path.exists(summary_folder):
            files = os.listdir(summary_folder)
            print(f"📁 โฟลเดอร์สรุป: {summary_folder}")
            print(f"📄 ไฟล์ที่พบ: {len(files)} ไฟล์")
            
            for file in sorted(files):
                file_path = os.path.join(summary_folder, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"   • {file} ({size} bytes)")
            
            # ตรวจสอบไฟล์สำคัญ
            master_file = os.path.join(summary_folder, "master_training_history.csv")
            if os.path.exists(master_file):
                df_master = pd.read_csv(master_file)
                print(f"✅ ไฟล์รวม: {len(df_master)} รายการ")
                
                # แสดงรายการล่าสุด
                if len(df_master) > 0:
                    latest = df_master.tail(3)
                    print("📊 รายการล่าสุด:")
                    for _, row in latest.iterrows():
                        print(f"   - {row['symbol']} M{row['timeframe']:03d} {row['scenario']}: Score {row['performance_score']:.1f}")
            else:
                print("⚠️ ไม่พบไฟล์รวม")
        else:
            print("⚠️ ไม่พบโฟลเดอร์สรุป")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการตรวจสอบไฟล์: {e}")
    
    print("\n✅ ทดสอบ Multi-Model Training Summary เสร็จสิ้น")
    print("=" * 60)
    
    return True

def test_multiple_symbols():
    """ทดสอบการเทรนหลาย symbols"""
    
    print("\n🔄 ทดสอบการเทรนหลาย symbols")
    print("=" * 40)
    
    try:
        from python_LightGBM_17_Signal import train_all_scenario_models
        
        symbols = ["GBPUSD", "EURUSD"]
        timeframes = [30, 60]
        
        for symbol in symbols:
            for timeframe in timeframes:
                print(f"\n🚀 เทรน {symbol} M{timeframe}")
                
                # สร้างข้อมูลตัวอย่าง
                df = create_sample_data_for_multi_model()
                
                # เทรนโมเดล
                results = train_all_scenario_models(
                    df=df,
                    symbol=symbol,
                    timeframe=timeframe,
                    target_column='Target_Multiclass'
                )
                
                if results and len(results) > 0:
                    print(f"✅ เทรน {symbol} M{timeframe} สำเร็จ: {len(results)} scenarios")
                else:
                    print(f"❌ เทรน {symbol} M{timeframe} ล้มเหลว")
        
        # ดูรายงานรวมหลังจากเทรนหลาย symbols
        print("\n📊 รายงานรวมหลังจากเทรนหลาย symbols:")
        from python_LightGBM_17_Signal import view_training_summary_reports
        view_training_summary_reports(show_details=True)
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบหลาย symbols: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 เริ่มทดสอบระบบสรุปผลการเทรน Multi-Model")
    print("=" * 60)
    
    # ทดสอบการเทรน Multi-Model
    success = test_multi_model_training()
    
    if success:
        # ทดสอบการเทรนหลาย symbols
        test_multiple_symbols()
        
        print("\n🎉 ทดสอบทั้งหมดเสร็จสิ้น!")
        print("\n💡 คำแนะนำ:")
        print("   1. ตรวจสอบโฟลเดอร์ training_summaries")
        print("   2. ดูไฟล์ .csv และ .txt ที่ถูกสร้างขึ้น")
        print("   3. ระบบสรุปทำงานอัตโนมัติใน Multi-Model แล้ว")
    else:
        print("\n❌ การทดสอบล้มเหลว - กรุณาตรวจสอบข้อผิดพลาด")
        print("\n🔧 วิธีแก้ไข:")
        print("   1. ตรวจสอบ USE_MULTI_MODEL_ARCHITECTURE = True")
        print("   2. ตรวจสอบการ import ฟังก์ชัน")
        print("   3. ตรวจสอบข้อมูลตัวอย่าง")
