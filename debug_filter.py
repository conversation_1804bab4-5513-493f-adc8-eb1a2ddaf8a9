#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Filter Script
สคริปต์ debug การกรองข้อมูลสำหรับระบบเปรียบเทียบ Entry Conditions
"""

import sys
import os

# เพิ่ม path สำหรับ import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_import():
    """ทดสอบการ import ฟังก์ชัน"""
    print("🔍 ทดสอบการ import ฟังก์ชัน")
    print("=" * 50)
    
    try:
        from python_LightGBM_18 import (
            ENTRY_CONFIGS,
            CURRENT_ENTRY_CONFIG,
            run_entry_config_comparison_test,
            run_main_analysis
        )
        print("✅ Import สำเร็จ")
        print(f"   CURRENT_ENTRY_CONFIG: {CURRENT_ENTRY_CONFIG}")
        print(f"   ENTRY_CONFIGS keys: {list(ENTRY_CONFIGS.keys())}")
        return True
    except ImportError as e:
        print(f"❌ Import ไม่สำเร็จ: {e}")
        return False

def debug_run_main_analysis():
    """ทดสอบฟังก์ชัน run_main_analysis"""
    print("\n🔍 ทดสอบฟังก์ชัน run_main_analysis")
    print("=" * 50)
    
    try:
        from python_LightGBM_18 import run_main_analysis
        
        # ตรวจสอบ signature ของฟังก์ชัน
        import inspect
        sig = inspect.signature(run_main_analysis)
        print(f"Function signature: {sig}")
        
        # ทดสอบการเรียกใช้ด้วยพารามิเตอร์
        print("\n🧪 ทดสอบการเรียกใช้ด้วยพารามิเตอร์:")
        print("run_main_analysis(filter_symbols=['GOLD'], filter_timeframes=[30])")
        
        # ไม่รันจริง เพียงแค่ตรวจสอบ syntax
        print("✅ Syntax ถูกต้อง")
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")

def debug_test_groups():
    """ตรวจสอบ test_groups"""
    print("\n🔍 ตรวจสอบ test_groups")
    print("=" * 50)
    
    try:
        from python_LightGBM_18 import test_groups
        
        print(f"test_groups keys: {list(test_groups.keys())}")
        
        for group_name, files in test_groups.items():
            print(f"\n{group_name}: {len(files)} ไฟล์")
            for file_path in files[:3]:  # แสดงเฉพาะ 3 ไฟล์แรก
                file_name = os.path.basename(file_path)
                symbol = file_name.split('_')[0]
                print(f"   {file_name} -> Symbol: {symbol}")
            if len(files) > 3:
                print(f"   ... และอีก {len(files) - 3} ไฟล์")
                
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")

def debug_filter_logic():
    """ทดสอบตรรกะการกรอง"""
    print("\n🔍 ทดสอบตรรกะการกรอง")
    print("=" * 50)
    
    # จำลอง test_groups
    test_groups = {
        "M30": [
            "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
            "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
            "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv"
        ],
        "M60": [
            "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
            "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
            "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv"
        ]
    }
    
    filter_symbols = ['GOLD']
    filter_timeframes = [30]
    
    print(f"Input:")
    print(f"   filter_symbols: {filter_symbols}")
    print(f"   filter_timeframes: {filter_timeframes}")
    
    filtered_test_groups = {}
    
    for group_name, group_files in test_groups.items():
        print(f"\n🔍 ตรวจสอบ {group_name}:")
        
        # ตรวจสอบ timeframe
        if filter_timeframes:
            timeframe_num = int(group_name[1:])  # M30 -> 30, M60 -> 60
            print(f"   timeframe_num: {timeframe_num}")
            if timeframe_num not in filter_timeframes:
                print(f"   ⏭️ ข้าม {group_name} (ไม่อยู่ใน timeframes ที่ระบุ)")
                continue
        
        # กรองไฟล์ตาม symbols
        filtered_files = []
        for file_path in group_files:
            file_name = os.path.basename(file_path)
            symbol = file_name.split('_')[0]
            print(f"   ตรวจสอบ {file_name} -> Symbol: {symbol}")
            
            if filter_symbols and symbol not in filter_symbols:
                print(f"     ⏭️ ข้าม (ไม่อยู่ใน symbols ที่ระบุ)")
                continue
            
            filtered_files.append(file_path)
            print(f"     ✅ รวม")
        
        if filtered_files:
            filtered_test_groups[group_name] = filtered_files
    
    print(f"\nผลลัพธ์:")
    for group_name, files in filtered_test_groups.items():
        print(f"   {group_name}: {len(files)} ไฟล์")
        for file_path in files:
            print(f"     - {os.path.basename(file_path)}")

def debug_entry_config_test():
    """ทดสอบการทำงานของ entry_config_test.py"""
    print("\n🔍 ทดสอบการทำงานของ entry_config_test.py")
    print("=" * 50)
    
    # จำลองการทำงานของ entry_config_test.py
    symbols = ['GOLD']
    timeframes = [30]
    
    print(f"จำลองคำสั่ง: --symbols {symbols} --timeframes {timeframes}")
    
    try:
        from python_LightGBM_18 import run_entry_config_comparison_test
        
        print("🧪 เรียกใช้ run_entry_config_comparison_test...")
        print(f"   พารามิเตอร์: symbols={symbols}, timeframes={timeframes}")
        
        # ไม่รันจริง เพียงแค่ตรวจสอบ
        print("✅ ฟังก์ชันพร้อมใช้งาน")
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")

def create_test_script():
    """สร้างสคริปต์ทดสอบง่าย"""
    print("\n🔧 สร้างสคริปต์ทดสอบง่าย")
    print("=" * 50)
    
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_filter():
    print("🧪 ทดสอบการกรองข้อมูล")
    
    try:
        from python_LightGBM_18 import run_main_analysis
        
        print("✅ Import run_main_analysis สำเร็จ")
        
        # ทดสอบการเรียกใช้
        print("🔍 ทดสอบการเรียกใช้ด้วยพารามิเตอร์:")
        print("run_main_analysis(filter_symbols=['GOLD'], filter_timeframes=[30])")
        
        # เรียกใช้จริง (แต่จะหยุดเร็วๆ)
        # result = run_main_analysis(filter_symbols=['GOLD'], filter_timeframes=[30])
        
        print("✅ ทดสอบเสร็จสิ้น")
        
    except Exception as e:
        print(f"❌ ข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_filter()
"""
    
    with open('test_quick.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ สร้างไฟล์ test_quick.py เสร็จสิ้น")
    print("💡 รันคำสั่ง: python test_quick.py")

def main():
    print("🐛 Debug Filter Script")
    print("=" * 50)
    
    # รันการทดสอบทั้งหมด
    if debug_import():
        debug_run_main_analysis()
        debug_test_groups()
        debug_filter_logic()
        debug_entry_config_test()
        create_test_script()
    
    print("\n✅ การ debug เสร็จสิ้น")
    print("\n💡 ขั้นตอนต่อไป:")
    print("1. รันคำสั่ง: python test_quick.py")
    print("2. ตรวจสอบข้อความ error")
    print("3. รันคำสั่ง: python entry_config_test.py --mode comparison --symbols GOLD --timeframes 30")

if __name__ == "__main__":
    main()
